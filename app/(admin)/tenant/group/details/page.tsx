import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { GroupMemberListScreen } from '@/components/wrapper-screen/group-member-listing';
import { authOptions } from '@/lib/auth';
import {
  getAllGroup,
  getDepartmentMembershipMappingByDepartmentId,
  getGroupMembershipMappingByGroupId,
  getStaffList,
} from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  const searchParams = props?.searchParams;
  const groupId = searchParams?.id;
  const groupName = searchParams?.key;

  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;
  const organizationType = 'organization';

  if (!organizationId) {
    return redirect('/404');
  }
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const [membersList, groupList, group] = await Promise.all([
    getStaffList({
      organizationId: organizationId,
      role: ['MEMBER'],
      userId: userId,
      organizationType: organizationType,
    }),
    getGroupMembershipMappingByGroupId({
      groupId,
      role: ['MEMBER', 'ADMIN'],
    }),
    getAllGroup({
      groupId,
    }),
  ]);
  const departmentAdminList = await getDepartmentMembershipMappingByDepartmentId({
    departmentId: group?.group?.[0]?.department?.id,
    role: ['ADMIN'],
  });

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const membersWithoutDepartmentMapping = membersList?.items?.filter(
    (item) =>
      !departmentAdminList?.departmentMembershipMapping?.some(
        (oldItem) => oldItem?.membershipId === item?.membershipId,
      ),
  );
  const currentUser = groupList?.groupMembershipMapping?.find(
    (item) => item?.membershipId === currentOrg?.id,
  );
  const currentDepartmentUser = departmentAdminList?.departmentMembershipMapping?.find(
    (item) => item?.membershipId === currentOrg?.id,
  );
  return (
    <GroupMemberListScreen
      userId={userId}
      groupId={groupId}
      members={membersWithoutDepartmentMapping}
      groupName={groupName}
      department={group?.group?.[0]?.department}
      groupMembers={groupList?.groupMembershipMapping}
      isSuperUser={
        isSuperUser || currentUser?.role === 'ADMIN' || currentDepartmentUser?.role === 'ADMIN'
      }
    />
  );
}
