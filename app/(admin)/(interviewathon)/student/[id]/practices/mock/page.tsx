import { EdgeCaseCard } from '@/components/cards/edge-case';
import MyInterviewCard from '@/components/cards/video-interview-card';
import { authOptions } from '@/lib/auth';
import { getUserInterviews } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview(props) {
  const id = props.params.id;

  const interviews = await getUserInterviews(id);

  if (interviews?.items?.length === 0) {
    return (
      <EdgeCaseCard
        title="No Mock Interviews"
        description="The student has not begun practicing yet."
      />
    );
  }

  const session = await getServerSession(authOptions);
  return (
    <div className="grid w-full grid-cols-1 gap-4">
      {interviews?.items?.map((interview, index) => (
        <MyInterviewCard interview={interview} key={index} session={session} />
      ))}
    </div>
  );
}
