'use client';

import { format } from 'date-fns';
import { CheckCircle, Clock, Send } from 'lucide-react';

import { Card, CardContent } from '@camped-ui/card';

interface InterviewTimelineProps {
  careerPractice: any;
}

export const InterviewTimeline = ({ careerPractice }: InterviewTimelineProps) => {
  // Calculate duration
  const getDuration = () => {
    const startTime = careerPractice?.timing?.startTime;
    const completedTime = careerPractice?.timing?.completedTime;

    if (startTime && completedTime) {
      const start = new Date(startTime);
      const end = new Date(completedTime);
      const diffInMinutes = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
      return `${diffInMinutes} min`;
    }
    return 'N/A';
  };

  // Timeline data preparation
  const getTimelineData = () => {
    const inviteTime = careerPractice?.timing?.inviteTime;
    const startTime = careerPractice?.timing?.startTime;
    const completedTime = careerPractice?.timing?.completedTime;

    const timeline: Array<{
      label: string;
      date: string;
      time: string;
      status: string;
      icon: React.ReactNode;
    }> = [];

    if (inviteTime) {
      timeline.push({
        label: 'Invited',
        date: format(new Date(inviteTime), 'MMM dd'),
        time: format(new Date(inviteTime), 'HH:mm'),
        status: 'completed',
        icon: <Send className="h-4 w-4" />,
      });
    }

    if (startTime) {
      timeline.push({
        label: 'Started',
        date: format(new Date(startTime), 'MMM dd'),
        time: format(new Date(startTime), 'HH:mm'),
        status: 'completed',
        icon: <Clock className="h-4 w-4" />,
      });
    }

    if (completedTime) {
      timeline.push({
        label: 'Completed',
        date: format(new Date(completedTime), 'MMM dd'),
        time: format(new Date(completedTime), 'HH:mm'),
        status: 'completed',
        icon: <CheckCircle className="h-4 w-4" />,
      });
    }

    return timeline;
  };

  const timelineData = getTimelineData();

  if (timelineData.length === 0) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardContent className="p-4">
        {/* Header */}
        {/* <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Interview Timeline
          </h3>
          <span className="text-sm text-gray-500">Duration: {getDuration()}</span>
        </div> */}

        {/* Timeline */}
        <div className="relative">
          {/* Status labels above timeline */}
          <div className="mb-2 flex items-center justify-between">
            {timelineData.map((item, index) => (
              <div key={index} className="flex min-w-[80px] flex-col items-center">
                <p className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  {item.label}
                </p>
              </div>
            ))}
          </div>

          {/* Timeline line and dots */}
          <div className="relative mb-2">
            {/* Background connecting line */}
            {timelineData.length > 1 && (
              <div className="absolute left-6 right-6 top-4 h-0.5 bg-blue-200 dark:bg-blue-800" />
            )}

            {/* Timeline dots */}
            <div className="flex items-center justify-between">
              {timelineData.map((item, index) => (
                <div key={index} className="relative z-10 flex min-w-[80px] flex-col items-center">
                  {/* Timeline dot */}
                  <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-blue-500 bg-blue-100 text-blue-600 dark:border-blue-400 dark:bg-blue-900 dark:text-blue-300">
                    {item.icon}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Date and time below timeline */}
          <div className="flex items-center justify-between">
            {timelineData.map((item, index) => (
              <div key={index} className="flex min-w-[80px] flex-col items-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {item.date} {item.time}
                </p>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
