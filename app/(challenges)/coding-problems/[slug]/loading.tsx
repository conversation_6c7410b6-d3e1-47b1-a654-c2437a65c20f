import CodeSkeleton from '@/components/skeleton/code';
import QuestionC<PERSON> from '@/components/skeleton/question-card';

import { Card } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  return (
    <div className="flex h-full w-full flex-col gap-4 md:flex-row">
      <div className="w-full max-w-[640px]" style={{ flex: 2 }}>
        <CodeSkeleton />
      </div>
      <Separator orientation="vertical" />
      <div style={{ flex: 1 }} className="w-full pb-4 md:max-w-[320px]">
        <Card className="h-8 w-3/5 border-none shadow-none">
          <Skeleton className="h-4my-2 w-3/5" />
        </Card>
        <Skeleton className="my-2 h-[25px] w-3/5" />
        <Skeleton className="my-2 h-[10px] w-full" />
        <Skeleton className="my-2 h-[10px] w-4/5" />
        <QuestionCard />
      </div>
    </div>
  );
}
