import { TopicScreen } from '@/components/wrapper-screen/quiz-topic-screen';
import { getQuizPracticeQuestion } from '@/services/apicall';

export default async function DemoPage(props) {
  const category = props?.params?.category;

  const quiz = await getQuizPracticeQuestion(category, '', 'gmat');
  const topic = [
    {
      id: 1,
      name: 'Verbal',
      slug: 'verbal',
      description: 'Enhance language skills, vital for interviews and aptitude tests.',
      isAuth: true,
      isPremium: false,
    },
    {
      id: 2,
      name: 'Quants',
      slug: 'quant',
      description: 'Enhance language skills, vital for interviews and aptitude tests.',
      isAuth: true,
      isPremium: false,
    },
    {
      id: 3,
      name: 'Data Insights',
      slug: 'data_insights',
      description: 'Enhance language skills, vital for interviews and aptitude tests.',
      isAuth: true,
      isPremium: false,
    },
  ];

  return (
    <TopicScreen
      quiz={quiz?.result}
      categoryName={null}
      topicName={topic?.find((topic) => topic?.slug === category)?.name}
    />
  );
}
