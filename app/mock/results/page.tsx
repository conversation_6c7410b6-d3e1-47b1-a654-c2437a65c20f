import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MockResultScreen } from '@/components/interview/mock/mock-result-screen';
import PageHeader from '@/components/page-header';
import { authOptions } from '@/lib/auth';
import { getUserInterviews } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewResults(props) {
  const session: any = await getServerSession(authOptions);

  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  if (!userId) {
    return redirect('/');
  }

  const id = props?.searchParams?.id;
  let interviewResult;
  const currentOrg = (session?.memberships as any)?.find(
    (item) => item?.organizationId === tenantId,
  );

  if (session?.memberships?.length > 0 && currentOrg?.role !== 'STUDENT') {
    interviewResult = await getUserInterviews(null, id);
  } else {
    interviewResult = await getUserInterviews(userId, id);
  }
  return (
    <div className="flex flex-col gap-4 p-4">
      <PageHeader hasBack title={interviewResult?.items?.question} />
      <MockResultScreen session={session} interviewResult={interviewResult} />
    </div>
  );
}
