import { MockQuestionScreen } from '@/components/interview/mock/mock-question-screen';
import { authOptions } from '@/lib/auth';
import { fetchQuestion, fetchQuestionType } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const session = await getServerSession(authOptions);

  const type = props?.params?.category;

  const initialQuestion = await fetchQuestion(type, session);

  const questionType = await fetchQuestionType();

  return (
    <MockQuestionScreen
      session={session}
      initialQuestion={initialQuestion}
      type={type}
      questionType={questionType}
    />
  );
}
