import React, { useCallback, useRef, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { Icon } from '@/icons';
import { createUserProfile, updateProfileImage } from '@/services/apicall';
import toast from 'react-hot-toast';
import Webcam from 'react-webcam';

import { Button } from '@camped-ui/button';

import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';

interface PhotoCaptureProps {
  session: any;
  onNext: () => void;
}

const PhotoCapture = ({ session, onNext }: PhotoCaptureProps) => {
  const webcamRef = useRef<Webcam | null>(null);
  const [cameraLoaded, setCameraLoaded] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const router = useRouter();
  const params = useSearchParams();

  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: 'user',
  };

  const handleUserMedia = () => {
    setTimeout(() => {
      setCameraLoaded(true);
      setCameraError(false);
    }, 1000);
  };

  const handleUserMediaError = (error: any) => {
    console.error('Camera error:', error);
    setCameraError(true);
    setCameraLoaded(false);
  };

  const capturePhoto = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      setCapturedImage(imageSrc);
    }
  }, [webcamRef]);

  const retakePhoto = () => {
    setCapturedImage(null);
  };

  const dataURLtoBlob = (dataURL: string) => {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  };

  const uploadPhoto = async () => {
    if (!capturedImage || !session?.userId) {
      console.error('Missing capturedImage or session.userId');
      return;
    }

    setIsUploading(true);
    toast.loading('Uploading photo...');

    try {
      // Convert base64 to blob
      const blob = dataURLtoBlob(capturedImage);
      const file = new File([blob], 'profile-photo.jpg', { type: 'image/jpeg' });

      // Get upload URL
      const res = await updateProfileImage(session.userId, 'user-profile', file.type);
      const { url } = res;

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (uploadResponse.ok) {
        // Update user profile
        await createUserProfile({
          userId: session.userId,
          updatedAt: new Date(),
        });

        toast.remove();
        toast.success('Photo uploaded successfully!');
        // Save progress to localStorage before proceeding
        if (typeof window !== 'undefined') {
          localStorage.setItem('interview-guidelines-step', '2');
        }
        onNext();
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.remove();
      toast.error('Failed to upload photo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const skipPhoto = () => {
    // Save progress to localStorage before proceeding
    if (typeof window !== 'undefined') {
      localStorage.setItem('interview-guidelines-step', '2');
    }
    onNext();
  };

  return (
    <div className="flex flex-col justify-center gap-8">
      {/* Progress Indicator */}
      <div className="mx-auto flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
            <Icon name="Check" className="h-4 w-4" />
          </div>
          <span className="text-sm text-green-600">Guidelines</span>
        </div>
        <div className="h-px w-12 bg-green-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
            2
          </div>
          <span className="text-sm font-medium text-blue-600">Photo</span>
        </div>
        <div className="h-px w-12 bg-gray-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm font-medium text-gray-500">
            3
          </div>
          <span className="text-sm text-gray-500">Setup</span>
        </div>
        <div className="h-px w-12 bg-gray-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm font-medium text-gray-500">
            4
          </div>
          <span className="text-sm text-gray-500">Interview</span>
        </div>
      </div>

      <OnboardingTemplate
        title="Capture Your Photo"
        description="Take a quick photo for your interview profile. This helps our team identify you during the process."
      />

      <div className="mx-auto max-w-md">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="relative mb-6">
            <div className="aspect-[4/3] w-full overflow-hidden rounded-lg bg-gray-100">
              {capturedImage ? (
                <img src={capturedImage} alt="Captured" className="h-full w-full object-cover" />
              ) : (
                <div className="relative h-full w-full">
                  {cameraError ? (
                    <div className="flex h-full flex-col items-center justify-center text-center">
                      <Icon name="AlertTriangle" className="mb-2 h-8 w-8 text-amber-500" />
                      <p className="text-sm text-muted-foreground">
                        Camera access denied or unavailable
                      </p>
                    </div>
                  ) : (
                    <>
                      <Webcam
                        ref={webcamRef}
                        audio={false}
                        screenshotFormat="image/jpeg"
                        videoConstraints={videoConstraints}
                        onUserMedia={handleUserMedia}
                        onUserMediaError={handleUserMediaError}
                        className="h-full w-full object-cover"
                        mirrored
                      />
                      {!cameraLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                          <Icon name="Loader2" className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            {capturedImage ? (
              <div className="flex gap-3">
                <Button onClick={retakePhoto} variant="outline" className="flex-1">
                  <Icon name="Camera" className="mr-2 h-4 w-4" />
                  Retake
                </Button>
                <Button onClick={uploadPhoto} disabled={isUploading} className="flex-1">
                  {isUploading && <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />}
                  Use Photo
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <Button
                  onClick={capturePhoto}
                  disabled={!cameraLoaded || cameraError}
                  className="w-full"
                  size="lg"
                >
                  <Icon name="Camera" className="mr-2 h-4 w-4" />
                  Take Photo
                </Button>
               
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoCapture;
