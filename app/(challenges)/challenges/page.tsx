'use client';

import Link from 'next/link';

import { DSAChallenges, frontendChallenges } from '@/constants/apps-metadata';
import { Icon } from '@/icons';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

export default function MyPractices() {
  return (
    <>
      <h3 className="scroll-m-20 text-2xl font-medium tracking-wide">
        Data Structures and Algorithms
      </h3>
      <p className="mb-2 leading-7 text-muted-foreground">
        Master data structures and algorithms to solve challenging problems and optimize code for
        efficient performance.
      </p>
      <Separator className="mb-4" />

      <div className="mt-4 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
        {DSAChallenges?.map((item, index) => (
          <Link
            href={`/${item?.path}?category=${item?.key}`}
            key={index}
            className="flex h-full w-full flex-1"
          >
            <ChallengesCard item={item} />
          </Link>
        ))}
      </div>

      <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">Web Development</h3>
      <p className="mb-2 leading-7 text-muted-foreground">
        Start practicing web development today to bring your ideas to life online and build dynamic,
        interactive experiences for users worldwide.
      </p>
      <Separator className="mb-4" />
      <div className="mt-4 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
        {frontendChallenges?.map((item, index) => (
          <Link
            href={`/${item?.path}?framework=${item?.key}`}
            key={index}
            className="flex h-full w-full flex-1"
          >
            <ChallengesCard item={item} />
          </Link>
        ))}
      </div>
    </>
  );
}

const textColors = {
  pink: 'text-[#C93AFB]',
  purple: 'text-[#6A57C7]',
  blue: 'text-[#5784C8]',
  turquoise: 'text-[#00A2C1]',
  lightpink: 'text-[#FF76DF]',
  orange: 'text-[#FF9900]',
  cyan: 'text-[#02CDFA]',
  green: 'text-[#7CBF63]',
  yellow: 'text-[#FECF23]',
};

const bgColors = {
  pink: 'bg-[#C93AFB]',
  purple: 'bg-[#6A57C7]',
  blue: 'bg-[#5784C8]',
  turquoise: 'bg-[#00A2C1]',
  lightpink: 'bg-[#FF76DF]',
  orange: 'bg-[#FF9900]',
  cyan: 'bg-[#02CDFA]',
  green: 'bg-[#7CBF63]',
  yellow: 'bg-[#FECF23]',
};

const ChallengesCard = ({ item }) => {
  let colorClasses = textColors[item?.iconBg];
  let bgClasses = bgColors[item?.iconBg];

  return (
    <Card className="flex w-full flex-row items-start gap-4 space-y-4 border-none p-4 shadow-none">
      <CardContent className="flex-1 p-0">
        <Card
          className={cn(
            'flex h-10 w-10 items-center justify-center border-0 bg-opacity-15 p-2',
            `${bgClasses ? bgClasses : 'bg-secondary'}`,
          )}
        >
          <Icon name={item?.iconName} className={cn('my-2', colorClasses)} />
        </Card>
        <CardTitle className="text-md mb-1 mt-2">{item?.name}</CardTitle>
        <CardDescription>{item?.description}</CardDescription>
      </CardContent>
    </Card>
  );
};
