import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

const calculateAverageScore = (feedbacks, field) => {
  const totalScore = feedbacks.reduce((total, practice) => {
    const score = practice.feedback?.[field] || 0;
    return total + score;
  }, 0);

  const average = feedbacks.length > 0 ? totalScore / feedbacks.length : 0;
  return average % 1 !== 0 ? average.toFixed(2) : average;
};

export async function POST(request: any) {
  const { organizationId, role, userId, searchParams } = await request.json();
  const { page = 1, per_page = 50, event, sort, user } = searchParams;
  const eventId =
    Array.isArray(event?.split('.')) || !event ? event?.split('.') : [event?.split('.')];

  if (!userId) {
    return NextResponse.json({ error: 'userDetails Required' }, { status: 404 });
  }

  const memberResponse = await db.membership.findFirst({
    where: {
      userId: userId,
      organizationId: organizationId,
      role: { in: ['OWNER', 'MEMBER', 'ADMIN'] },
    },
  });

  if (!memberResponse) {
    return NextResponse.json({ error: 'Your are not part of Organization' }, { status: 400 });
  }

  if (!organizationId) {
    return NextResponse.json({ error: 'Your are not part of Organization' }, { status: 404 });
  }
  const parsedLimit = parseInt(per_page, 50);
  const events = await db.eventDetails.findMany({
    where: {
      organizationId: organizationId,
    },
    select: {
      id: true,
    },
  });

  const eventList = eventId?.length > 0 ? eventId : events?.map((item) => item.id);

  const where: any = {
    organizationId,
    role: { in: role },
  };
  if (eventId?.length > 0) {
    where.user = {
      careerPractices: {
        some: {
          eventId: { in: eventList },
        },
      },
    };
  } else if (user) {
    where.user = {
      email: {
        contains: user,
        mode: 'insensitive',
      },
    };
  }

  const memberDetails = await db.membership.findMany({
    where,
    include: {
      user: {
        select: {
          email: true,
          userProfile: true,
          name: true,
          careerPractices: {
            select: {
              id: true,
              feedback: true,
              eventId: true,
              timing: true,
              eventDetails: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            where: {
              eventId: { in: eventList },
            },
          },
        },
      },
    },
    skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
    take: Number(per_page ?? 50),
    orderBy: {
      createdAt: 'desc',
    },
  });

  const count = await db.membership.count({
    where,
  });

  memberDetails.map((item) => {
    const careerPractices = item?.user?.careerPractices || [];
    const totalUnansweredPractices = careerPractices.filter((practice) => {
      const practiceQuestions = [];
      return (practiceQuestions as any)?.some((question) => !question.isAnswered);
    }).length;
    const notStartedTest = careerPractices.filter((practice: any) => {
      return !practice?.timing?.startTime;
    }).length;

    const notCompletedTest = careerPractices.filter((practice: any) => {
      return practice?.timing?.startTime && !practice?.timing?.completedTime;
    }).length;

    const completedTest = careerPractices.length - notCompletedTest - notStartedTest;

    const practicesWithFeedback = careerPractices.filter((practice) => practice.feedback);

    const averageOverallScore = calculateAverageScore(practicesWithFeedback, 'overall_score');
    const averageCommunicationScore = calculateAverageScore(practicesWithFeedback, 'communication');
    const averageConfidenceScore = calculateAverageScore(practicesWithFeedback, 'confidence');
    const averageLanguageProficiencyScore = calculateAverageScore(
      practicesWithFeedback,
      'language_proficiency',
    );

    const averageProblemSolvingScore = calculateAverageScore(
      practicesWithFeedback,
      'problem_solving',
    );

    const averageEfficiencyScore = calculateAverageScore(practicesWithFeedback, 'efficiency');

    const averageAlgorithmChoiceScore = calculateAverageScore(
      practicesWithFeedback,
      'algorithm_choice',
    );

    const averageFrontendChoiceScore = calculateAverageScore(
      practicesWithFeedback,
      'frontend_choice',
    );

    // Extract event names from the careerPractices
    const eventNames = careerPractices
      .map((practice) => practice.eventDetails?.name)
      .filter(Boolean);

    const answeredQuestions = careerPractices.length - totalUnansweredPractices;
    item['interviewAttended'] = `${answeredQuestions} / ${careerPractices.length}`;

    item['notStartedInterviews'] = `${notStartedTest} / ${careerPractices.length}`;
    item['startedInterviews'] = `${notCompletedTest} / ${careerPractices.length}`;
    item['completedInterviews'] = `${completedTest} / ${careerPractices.length}`;

    item['performance'] = averageOverallScore || 0;
    item['communication'] = averageCommunicationScore || 0;
    item['confidence'] = averageConfidenceScore || 0;
    item['language'] = averageLanguageProficiencyScore || 0;
    item['problemSolving'] = averageProblemSolvingScore || 0;
    item['efficiency'] = averageEfficiencyScore || 0;
    item['algorithmChoice'] = averageAlgorithmChoiceScore || 0;
    item['frontendChoice'] = averageFrontendChoiceScore || 0;
    item['event'] = eventNames;
    item['resumeLink'] = item?.user?.userProfile?.resumeS3Id
      ? `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/resume/${item?.user?.userProfile?.resumeS3Id}`
      : '';

    return item;
  });
  return NextResponse.json(
    {
      items: memberDetails,
      count: count,
      currentPage: Math.max(page, 1),
      limit: parsedLimit,
      totalPages: Math.ceil(count / Number(per_page)),
    },
    { status: 200 },
  );
}
