import { NextResponse } from 'next/server';

import { getSignedUrl } from '@/pages/api/gcp-bucket';
import { db } from '@/prisma/db';

function removeFieldsFromConversation(inputObject) {
  const fieldsToRemove = [
    'impact',
    'clarity',
    'passion',
    'strengths',
    'confidence',
    'communication',
    'areas_of_improvement',
    'language_proficiency',
  ];

  for (const item of inputObject.conversation) {
    for (const field of fieldsToRemove) {
      if (item.feedback && item.feedback[field]) {
        delete item.feedback[field];
      }
    }
  }
  return inputObject;
}

async function getUnansweredQuestions(careerPractice) {
  const unansweredQuestions = careerPractice?.conversation.filter((item) => !item.isAnswered);

  return unansweredQuestions;
}

export async function POST(request: any) {
  const { id, sub, isAdmin } = (await request.json()) as {
    id?: string;
    sub?: string;
    isAdmin?: boolean;
  };
  let where: any = { id };
  if (!isAdmin) {
    where.userId = sub;
  }
  const careerPractice = await db.careerPractice.findFirst({
    where,
    include: {
      eventDetails: true,
    },
  });

  if (!careerPractice) {
    return NextResponse.json(
      {
        error: 'Invalid id',
      },
      { status: 400 },
    );
  }

  const unansweredQuestions = await getUnansweredQuestions(careerPractice);

  if (unansweredQuestions?.length === 0) {
    // Assuming careerPractice is your data structure containing conversation
    const conversation = careerPractice?.conversation;

    const mappedResponses = await Promise.all(
      (Array.isArray(conversation) ? conversation : [])?.map(async (response) => {
        try {
          if (typeof response === 'object' && response !== null && 's3Id' in response) {
            let videoUrl;
            if (response?.videoOrigin === 'GCP_BUCKET') {
              videoUrl = await getSignedUrl(
                response.s3Id,
                careerPractice?.eventDetails?.organizationId,
              );
            } else {
              videoUrl = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/${response.s3Id}`;
            }
            return {
              ...response,
              videoUrl,
            };
          } else {
            // Handle cases where response or response.s3Id is missing or falsy.
            // You can choose to return some default value or handle the error accordingly.
            if (typeof response === 'object' && response !== null) {
              return {
                ...response,
                videoUrl: null, // or any default value
              };
            } else {
              return {
                videoUrl: null,
              };
            }
          }
        } catch (error) {
          // Handle errors that occur while fetching the pre-signed URL.
          // You can choose to throw the error, log it, or handle it as needed.
          console.error(`Error fetching pre-signed URL for response:`, error);
          throw error; // Optionally rethrow the error if needed.
        }
      }),
    );

    return NextResponse.json(
      {
        status: 'success',
        message: 'Your interview session has been completed successfully.',
        event: careerPractice?.event,
        role: careerPractice?.role,
        result: { ...careerPractice, conversation: mappedResponses },
      },
      { status: 200 },
    );
  }

  return NextResponse.json(
    {
      id: careerPractice?.id,
      message: 'Partially Completed',
      event: careerPractice?.event,
      result: {
        role: careerPractice?.role,
        level: careerPractice?.level,
      },
      hasCompleted: false,
    },
    { status: 200 },
  );
}
