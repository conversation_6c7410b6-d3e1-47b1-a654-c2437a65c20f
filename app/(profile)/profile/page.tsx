import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { ProfileScreen } from '@/components/wrapper-screen/profile-screen';
import { authOptions } from '@/lib/auth';
import { getUserDetails } from '@/services/apicall';
import { getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyProfile({}) {
  const session = await getServerSession(authOptions);
  const userId = cookies()?.get('aceprepUserId')?.value;

  if (!userId) {
    return redirect('/404');
  }
  const user = await getUserDetails(userId);
  const userProfile = await getUserProfile(userId);
  return <ProfileScreen user={user} userProfile={userProfile} userId={userId} session={session} />;
}
