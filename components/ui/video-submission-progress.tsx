import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@/icons';
import { Button } from '@camped-ui/button';
import { Progress } from '@camped-ui/progress';

interface VideoSubmissionProgressProps {
  isUploading: boolean;
  uploadProgress: number;
  isProcessingFeedback: boolean;
  currentStep: 'idle' | 'compressing' | 'uploading' | 'processing-feedback' | 'completed' | 'error';
  error?: string | null;
  onCancel?: () => void;
  onRetry?: () => void;
  className?: string;
}

const stepLabels = {
  idle: 'Ready to submit',
  compressing: 'Optimizing video...',
  uploading: 'Uploading video...',
  'processing-feedback': 'Analyzing video with AI...',
  completed: 'Analysis completed!',
  error: 'Submission failed',
};

const stepIcons = {
  idle: 'Play',
  compressing: 'Loader2',
  uploading: 'Upload',
  'processing-feedback': 'Brain',
  completed: 'CheckCircle',
  error: 'AlertCircle',
};

const stepDescriptions = {
  idle: 'Click submit to start processing your video answer',
  compressing: 'Optimizing video size for faster upload',
  uploading: 'Securely uploading your video to our servers',
  'processing-feedback': 'AI is analyzing your response and generating feedback',
  completed: 'Your video has been processed and feedback is ready',
  error: 'Something went wrong during processing',
};

export const VideoSubmissionProgress: React.FC<VideoSubmissionProgressProps> = ({
  isUploading,
  uploadProgress,
  isProcessingFeedback,
  currentStep,
  error,
  onCancel,
  onRetry,
  className = '',
}) => {
  if (currentStep === 'idle') {
    return null;
  }

  const isProcessing = isUploading || isProcessingFeedback;
  const overallProgress = currentStep === 'compressing' 
    ? 10 // Compression is quick
    : currentStep === 'uploading'
    ? 10 + (uploadProgress * 0.6) // Upload is 60% of visible progress
    : currentStep === 'processing-feedback'
    ? 70 // Show 70% while processing feedback
    : currentStep === 'completed'
    ? 100
    : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`rounded-lg border bg-card p-4 shadow-sm ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <Icon
            name={stepIcons[currentStep]}
            className={`h-5 w-5 ${
              currentStep === 'compressing' || currentStep === 'uploading' || currentStep === 'processing-feedback'
                ? 'animate-spin text-blue-500'
                : currentStep === 'completed'
                ? 'text-green-500'
                : currentStep === 'error'
                ? 'text-red-500'
                : 'text-gray-500'
            }`}
          />
          <div>
            <span className="font-medium text-sm">
              {stepLabels[currentStep]}
            </span>
            <p className="text-xs text-muted-foreground">
              {stepDescriptions[currentStep]}
            </p>
          </div>
        </div>

        {isProcessing && onCancel && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 px-2"
          >
            <Icon name="X" className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Overall Progress Bar */}
      {(isProcessing || currentStep === 'completed') && (
        <div className="mb-3">
          <Progress value={overallProgress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{Math.round(overallProgress)}% complete</span>
            {currentStep === 'uploading' && (
              <span>Upload: {uploadProgress}%</span>
            )}
          </div>
        </div>
      )}

      {/* Step-specific Progress */}
      {currentStep === 'uploading' && (
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Uploading to secure servers...</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-1" />
        </div>
      )}

      {currentStep === 'processing-feedback' && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-xs">
            <Icon name="Brain" className="h-3 w-3 animate-pulse text-purple-500" />
            <span>AI is analyzing your video response...</span>
          </div>
          <div className="bg-gradient-to-r from-purple-100 to-blue-100 h-1 rounded-full">
            <div className="bg-gradient-to-r from-purple-500 to-blue-500 h-1 rounded-full animate-pulse w-3/4"></div>
          </div>
        </div>
      )}

      {/* Error State */}
      {currentStep === 'error' && (
        <div className="space-y-3">
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
            <div className="flex items-start space-x-2">
              <Icon name="AlertCircle" className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">Upload Failed</p>
                <p className="text-xs mt-1">{error || 'An error occurred during video processing'}</p>
              </div>
            </div>
          </div>
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="w-full"
            >
              <Icon name="RotateCcw" className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </div>
      )}

      {/* Success State */}
      {currentStep === 'completed' && (
        <div className="text-sm text-green-600 bg-green-50 p-3 rounded">
          <div className="flex items-center space-x-2">
            <Icon name="CheckCircle" className="h-4 w-4" />
            <span>Video successfully uploaded and analyzed!</span>
          </div>
        </div>
      )}

      {/* Background Processing Info */}
      {currentStep === 'processing-feedback' && (
        <div className="mt-3 text-xs text-muted-foreground bg-blue-50 p-3 rounded">
          <div className="flex items-start space-x-2">
            <Icon name="Info" className="h-3 w-3 mt-0.5 flex-shrink-0 text-blue-500" />
            <div>
              <p className="font-medium text-blue-700 mb-1">AI Analysis in Progress</p>
              <p>Our AI is analyzing your video response to provide detailed feedback. This may take a few moments.</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Tips */}
      {currentStep === 'uploading' && (
        <div className="mt-3 text-xs text-muted-foreground">
          <div className="flex items-start space-x-1">
            <Icon name="Wifi" className="h-3 w-3 mt-0.5 flex-shrink-0" />
            <span>
              Large videos are uploaded in chunks for reliability. Keep this tab open for best results.
            </span>
          </div>
        </div>
      )}
    </motion.div>
  );
};

// Compact version for minimal UI space
export const VideoSubmissionProgressCompact: React.FC<VideoSubmissionProgressProps> = ({
  isUploading,
  isProcessingFeedback,
  currentStep,
  uploadProgress,
  onCancel,
}) => {
  if (currentStep === 'idle') {
    return null;
  }

  const isProcessing = isUploading || isProcessingFeedback;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm"
    >
      <Icon
        name={stepIcons[currentStep]}
        className={`h-4 w-4 ${
          isProcessing ? 'animate-spin' : ''
        }`}
      />
      <span className="flex-1">
        {stepLabels[currentStep]}
        {currentStep === 'uploading' && ` (${uploadProgress}%)`}
      </span>
      {isProcessing && onCancel && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="h-6 w-6 p-0"
        >
          <Icon name="X" className="h-3 w-3" />
        </Button>
      )}
    </motion.div>
  );
};
