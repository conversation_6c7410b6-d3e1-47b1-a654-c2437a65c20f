import { cookies } from 'next/headers';

import { TailorIdScreen } from '@/components/interview/tailored-practice/tailored-id-screen';
import { authOptions } from '@/lib/auth';
import { getTailoredTest } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props.params.id;

  const session = await getServerSession(authOptions);
  const userId = cookies()?.get('aceprepUserId')?.value;

  const careerPractice = await getTailoredTest({
    id: id,
    sub: userId || 'Visitor',
  });

  const practiceId = props.params.id;
  return (
    <TailorIdScreen
      careerPractice={careerPractice}
      practiceId={practiceId}
      session={session}
      userId={userId || 'Visitor'}
      screen="competitive-interviews"
    />
  );
}
