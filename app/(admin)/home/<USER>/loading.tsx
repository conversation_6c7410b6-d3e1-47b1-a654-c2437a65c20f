import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
import { SVGSkeleton, Skeleton } from '@/components/skeleton/skeleton';

export default function Loading() {
  const data = [1, 2, 3, 4, 5];
  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
      {data?.map((item, index) => (
        <div key={index} className="rounded-md border p-3 shadow-sm">
          <h3 className="tracking-tight">
            <div className="flex items-start justify-between">
              <Skeleton className="w-[144px] max-w-full" />
              <div className="flex justify-center">
                <SVGSkeleton className="h-[24px] w-[24px]" />
              </div>
            </div>
          </h3>
          <h3 className="mt-4 leading-none tracking-tight">
            <Skeleton className="w-[14px] max-w-full" />
          </h3>
          <p>
            <Skeleton className="w-[80px] max-w-full" />
          </p>
        </div>
      ))}
    </div>
  );
}
