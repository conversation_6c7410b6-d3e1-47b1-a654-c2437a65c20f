import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import HomeTopCard from '@/components/cards/home-top-card';
import { authOptions } from '@/lib/auth';
import { getInterviewathonOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const filter = props?.searchParams?.filter ?? 'last1hour';

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};
  const interviewOverview = await getInterviewathonOverview(tenantId, filter);

  return (
    <HomeTopCard
      data={[
        {
          label: 'Interviewathon Participation',
          value: interviewOverview?.count?.totalParticipation ?? 0,
          icon: 'CalendarRange',
          bottomLabel: 'Participation',
        },
        {
          label: 'Completed',
          value: interviewOverview?.count?.completedCount ?? 0,
          icon: 'CircleCheck',
          bottomLabel: 'Students',
        },
        {
          label: 'Partially completed',
          value: interviewOverview?.count?.partialCount ?? 0,
          icon: 'CircleHelp',
          bottomLabel: 'Students',
        },
      ]}
    />
  );
}
