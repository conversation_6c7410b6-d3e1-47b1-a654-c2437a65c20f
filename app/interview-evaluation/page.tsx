import Header from '@/components/Header';
import PageHeader from '@/components/page-header';
import { InterviewEvaluationScreen } from '@/components/wrapper-screen/interview-evaluation-screen';
import { VideoInterviewDetailScreen } from '@/components/wrapper-screen/interview/video-interview-detail-screen';
import { ViewInterviewDetailScreen } from '@/components/wrapper-screen/interview/view-interview-detail-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails, getUserInterviewDetails, getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Card, CardDescription, CardTitle } from '@camped-ui/card';

export default async function DemoPage(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;
  const hasBack = props?.searchParams?.hasBack === 'true';
  const isAdmin = props?.searchParams?.isAdmin === 'true';
  let response;
  if (isAdmin) {
    response = await getInterviewDetails(id);

    return (
      <ViewInterviewDetailScreen
        careerPractice={response?.result}
        userProfile={response?.userProfile}
        user={response?.user}
        userId={session?.userId}
        members={[]}
        platform=""
        isPublic={isAdmin}
      />
    );
  }

  response = await getUserInterviewDetails(id, session?.userId);

  return (
    <>
      <Header data={session} showMenus={false} />
      <div className="mx-auto mt-[85px] flex w-full flex-col gap-4 p-4 lg:flex-row lg:justify-center">
        <div className="flex w-full max-w-[900px] flex-col gap-4">
          <PageHeader
            hasBack={hasBack}
            title={`Performance Analysis - ${
              response?.items?.user?.userProfile?.fullName === '' ||
              !response?.items?.user?.userProfile?.fullName
                ? response?.items?.user?.email
                : response?.items?.user?.userProfile?.fullName
            }`}
          />
          {!response.items?.conversation?.[0]?.room_name ? (
            <InterviewEvaluationScreen careerPractice={response.items} session={{ ...session }} />
          ) : (
            <VideoInterviewDetailScreen
              meeting={{ ...response.items, s3RecordingId: response.items.conversation?.[0]?.s3Id }}
              userId={''}
              isAiInterview={true}
            />
          )}
        </div>
        <div className="mt-[45px] flex max-w-[320px] flex-col gap-4">
          {response?.items?.comments?.[0]?.status ? (
            <div className="flex items-start gap-2">
              <CardTitle className="text-xl">Status:</CardTitle>
              <CardDescription
                className={`text-xl font-semibold ${
                  response?.items?.comments?.[0]?.status === 'Accepted'
                    ? 'text-green-600'
                    : 'text-red-500'
                }`}
              >
                {response?.items?.comments?.[0]?.status}
              </CardDescription>
            </div>
          ) : null}
          <Card className="p-4">
            {response?.items?.event ? (
              <CardDescription className="mb-2 text-left">
                <span className="font-bold">Event:</span> {response?.items?.event}
              </CardDescription>
            ) : null}

            <CardDescription className="mb-2">
              <span className="font-bold">Role:</span> {response?.items?.role}
            </CardDescription>
            <CardDescription>
              <span className="font-bold">Level:</span> {response?.items?.level}
            </CardDescription>
          </Card>
        </div>
      </div>
    </>
  );
}
