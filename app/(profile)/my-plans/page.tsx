import React from 'react';

import { redirect } from 'next/navigation';

import MyPlansScreen from '@/components/wrapper-screen/my-plans-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function MyProfile() {
  const session: any = await getServerSession(authOptions);

  if (process.env.NEXT_PUBLIC_PLATFORM === 'hire' || session?.memberships?.length > 0) {
    redirect('/404');
  }
  return <MyPlansScreen data={session} />;
}
