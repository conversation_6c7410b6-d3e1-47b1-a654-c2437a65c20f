import { cookies } from 'next/headers';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { AddStudentScreen } from '@/components/wrapper-screen/student-details-screen/add-student';
import { authOptions } from '@/lib/auth';
import { getAllDepartment, getAllGroup, getStudentCount } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent() {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  const [studentCount, department, group]: any = await Promise.all([
    getStudentCount(tenantId),
    getAllDepartment({
      organizationId: tenantId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
    getAllGroup({
      organizationId: tenantId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);

  if (
    currentOrg?.organization?.eventLimit?.student <= studentCount?.studentCounts &&
    currentOrg?.organization?.eventLimit?.student !== null
  ) {
    return (
      <EdgeCaseCard
        title="Student Creation Limit Reached"
        description="The maximum number of Student has been added. Please contact us for further assistance."
        ctaLabel="Contact Us"
        ctaHref="/contact"
      />
    );
  }

  return (
    <AddStudentScreen
      department={department}
      group={group}
      limit={
        currentOrg?.organization?.eventLimit?.student
          ? currentOrg?.organization?.eventLimit?.student - studentCount?.studentCounts
          : null
      }
    />
  );
}
