import { cookies } from 'next/headers';

import { WebhookTable } from '@/components/data-table/webhook/data-table';
import { getWebhooks } from '@/services/apicall';

export default async function Integration(props) {
  const searchParams = props.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const webhooks = await getWebhooks({ organizationId: tenantId, searchParams });

  return (
    <WebhookTable tasksPromise={{ data: webhooks?.integration, pageCount: webhooks?.pageCount }} />
  );
}
