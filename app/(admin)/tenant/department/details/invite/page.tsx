import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { DepartmentMemberBulkInviteScreen } from '@/components/department/member-bulk-invite';
import { getStaffList } from '@/services/apicall';

export default async function ListEvent(props) {
  const departmentId = props?.searchParams?.id;
  const departmentName = props?.searchParams?.key;
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId) {
    return redirect('/404');
  }
  const [members] = await Promise.all([
    getStaffList({
      organizationId: organizationId,
      role: ['MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
  ]);

  return (
    <DepartmentMemberBulkInviteScreen
      members={members?.items}
      departmentName={departmentName}
      departmentId={departmentId}
    />
  );
}
