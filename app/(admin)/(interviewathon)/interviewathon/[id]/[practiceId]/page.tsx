import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { ViewInterviewDetailScreen } from '@/components/wrapper-screen/interview/view-interview-detail-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ViewInterviewDetail(props) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const tenantId = cookies()?.get('aceprepTenantId')?.value;
  const id = props.params.practiceId;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const response = await getInterviewDetails(id, currentOrg?.id, currentOrg?.role);
  if (!response) {
    return <NotFound />;
  }
  return (
    <ViewInterviewDetailScreen
      careerPractice={response?.result}
      userProfile={response?.userProfile}
      user={response?.user}
      userId={userId}
      members={{}}
      platform={''}
    />
  );
}
