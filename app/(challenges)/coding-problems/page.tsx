import { cookies } from 'next/headers';

import { CodingTable } from '@/components/data-table/coding-problems/data-table';
import { getCodingProblem, getCompany } from '@/services/apicall';

export type DocumentsPageProps = {
  searchParams?: {
    page?: string;
    per_page?: string;
    eventName?: string;
  };
};

export default async function MyProfile({ searchParams = {} }: DocumentsPageProps) {
  const [problems, company] = await Promise.all([getCodingProblem({ searchParams }), getCompany()]);
  const userId = cookies()?.get('aceprepUserId')?.value;

  return <CodingTable tasksPromise={{ data: problems }} userId={userId} company={company} />;
}
