import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MockInterviewQuestionScreen } from '@/components/interview/mock/mock-interview-question-screen';
import { authOptions } from '@/lib/auth';
import { fetchQuestionById } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const type = props?.params?.category;

  const question = props?.params?.id;
  const session = await getServerSession(authOptions);

  const interviewQuestion = await fetchQuestionById(type, question, session);

  return (
    <MockInterviewQuestionScreen
      interviewQuestion={interviewQuestion}
      type={type}
      session={session}
    />
  );
}
