import { cookies } from 'next/headers';
import Link from 'next/link';

import { getMyInterview } from '@/services/apicall';

import { Button } from '@camped-ui/button';
import { Card, CardDescription, CardFooter, CardTitle } from '@camped-ui/card';

export default async function App(props) {
  const userId = cookies().get('aceprepUserId')?.value;

  const myInterviews = (await getMyInterview(userId)) ?? [];

  if (myInterviews?.length === 0) return;
  return (
    <>
      <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">My Interviews</h3>
      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {myInterviews?.map((item, index) => (
          <Link key={index} href={`/onboarding-interview?id=${item?.id}`}>
            <Card key={index} className="border-none p-4 shadow-none">
              <CardTitle>{item?.eventDetails?.name}</CardTitle>
              <CardDescription className="mt-2">Role: {item?.eventDetails?.role}</CardDescription>
              <CardDescription className="mt-2">
                Company: {item?.eventDetails?.organization?.name}
              </CardDescription>
              <CardFooter className="flex items-end p-0">
                <Button variant="link" className="w-full justify-end p-0">
                  Take Interview{' '}
                </Button>
              </CardFooter>
            </Card>
          </Link>
        ))}
      </div>
    </>
  );
}
