import Header from '@/components/Header';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Layout({ children }) {
  const session = await getServerSession(authOptions);
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden`}
        style={{ paddingBottom: 88 }}
      >
        <Header data={session} />
        <div className="flex w-full flex-col md:flex-row">
          <div className="flex min-h-[60vh] w-full flex-col items-center justify-center px-4 pb-8 pt-2 md:px-0 md:py-2">
            <div className="mt-[100px] flex w-full flex-col items-center">
              <div className="flex flex-col space-y-2 text-center">
                <h1
                  className={`mt-[100px] text-3xl font-bold leading-tight tracking-tighter sm:text-3xl md:text-5xl lg:text-6xl`}
                >
                  Feel free to reach out!
                </h1>
                <p className="text-md max-w-[360px] self-center text-muted-foreground">
                  Got questions, feedback, or a friendly hello? Feel free to fill out the form
                  below.
                </p>
              </div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
