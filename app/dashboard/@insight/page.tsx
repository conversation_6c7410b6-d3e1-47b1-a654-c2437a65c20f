import Link from 'next/link';

import { getInsights } from '@/services/apicall';

import { Button } from '@camped-ui/button';
import { Card, CardFooter } from '@camped-ui/card';

export default async function App(props) {
  const insights = await getInsights();

  return (
    <>
      {insights?.map((item, index) => (
        <Link href={item?.navigationPath} key={index}>
          <Card key={index} className="border-none p-4 shadow-none">
            {item?.title}
            <CardFooter className="p-0">
              <Button variant="link" className="p-0">
                {item?.buttonLabel}
              </Button>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </>
  );
}
