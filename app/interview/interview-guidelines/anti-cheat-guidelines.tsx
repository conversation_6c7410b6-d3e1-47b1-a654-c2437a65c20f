import React, { useState } from 'react';

import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';
import { Checkbox } from '@camped-ui/checkbox';

import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';

interface AntiCheatGuidelinesProps {
  onNext: () => void;
}

const AntiCheatGuidelines = ({ onNext }: AntiCheatGuidelinesProps) => {
  const [acknowledged, setAcknowledged] = useState(false);

  const handleContinue = () => {
    if (acknowledged) {
      // Save progress to localStorage before proceeding
      if (typeof window !== 'undefined') {
        localStorage.setItem('interview-guidelines-step', '1');
      }
      onNext();
    }
  };

  return (
    <div className="flex flex-col justify-center gap-8">
      {/* Progress Indicator */}
      <div className="mx-auto flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
            1
          </div>
          <span className="text-sm font-medium text-blue-600">Guidelines</span>
        </div>
        <div className="h-px w-8 bg-gray-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm font-medium text-gray-500">
            2
          </div>
          <span className="text-sm text-gray-500">Photo</span>
        </div>
        <div className="h-px w-8 bg-gray-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm font-medium text-gray-500">
            3
          </div>
          <span className="text-sm text-gray-500">Setup</span>
        </div>
        <div className="h-px w-8 bg-gray-300"></div>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-sm font-medium text-gray-500">
            4
          </div>
          <span className="text-sm text-gray-500">Interview</span>
        </div>
      </div>

      <OnboardingTemplate
        title="Fair Assessment Guidelines"
        description="Our AI-powered proctoring ensures a fair and secure interview experience for all candidates"
      />

      <div className="mx-auto max-w-2xl">
        <div className="rounded-lg border bg-card p-4 shadow-sm">
       
        
          {/* Guidelines List */}
          <div className="space-y-4 text-left">
            <div className="rounded-lg bg-muted/50 p-2">
              <h3 className="mb-3 font-medium text-foreground">Please ensure you:</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                  <span className="mt-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                  Work independently without external assistance or resources
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                  Remain alone in the room throughout the interview
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                  Keep your eyes focused on the screen during the assessment
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                  Avoid browsing other websites or opening additional applications
                </li>
                
                <li className="flex items-start gap-2">
                  <span className="mt-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                  Most importantly, Do not use any GPT tools to answer. Your answers will be
                  validated against GPT models to check for plagiarism.
                </li>
              </ul>
            </div>

            <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/10">
              <div className="flex items-start gap-3">
                <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                  <Icon name="Eye" className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="mb-1 font-medium text-blue-900 dark:text-blue-100">
                    AI Vision Monitoring
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Our intelligent proctoring system continuously monitors your environment, eye
                    movement, and behavior to ensure assessment authenticity while respecting your
                    privacy.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Acknowledgment Checkbox */}
          <div className="mt-8 flex items-start gap-3">
            <Checkbox
              id="acknowledge"
              checked={acknowledged}
              onCheckedChange={(checked) => setAcknowledged(checked === true)}
              className="mt-1"
            />
            <label
              htmlFor="acknowledge"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I agree to maintain interview integrity and understand the monitoring guidelines
            </label>
          </div>
        </div>
      </div>

      {/* Next Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleContinue}
          disabled={!acknowledged}
          size="lg"
          className="min-w-[200px]"
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default AntiCheatGuidelines;
