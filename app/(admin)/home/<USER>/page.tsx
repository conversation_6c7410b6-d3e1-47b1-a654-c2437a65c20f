import { cookies } from 'next/headers';

import HomeBottomCard from '@/components/cards/home-bottom-card';
import { authOptions } from '@/lib/auth';
import { getInterviewDetail } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const filter = props?.searchParams?.filter ?? 'last1hour';
  const feedbackPage = props?.searchParams?.feedbackPage ?? 1;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [feedbackCompletedInterview] = await Promise.all([
    getInterviewDetail(
      tenantId,
      'feedbackNotSent',
      filter,
      feedbackPage,
      currentOrg?.id,
      currentOrg?.role,
    ),
  ]);

  return (
    <HomeBottomCard
      title={'Pending Feedback'}
      totalCount={feedbackCompletedInterview?.totalCount}
      screen="interviews"
      tabs={[
        {
          header: ['Candidate/Interview', 'Score', 'Completed Time'],
          row: feedbackCompletedInterview?.eventDetails?.map((item) => {
            return {
              id: item?.id,
              eventId: item?.eventDetails?.id,
              interview: item?.eventDetails?.name,
              score: item?.feedback?.overall_score,
              recommendation: item?.feedback?.overall_recommendation?.decision,
              candidate:
                item?.user?.userProfile?.fullName !== '' || item?.user?.userProfile?.fullName
                  ? item?.user?.userProfile?.fullName
                  : item?.user?.email,
              time: item?.completedTime,
            };
          }),
        },
      ]}
      page={feedbackPage}
    />
  );
}
