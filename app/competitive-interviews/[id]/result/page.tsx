import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { TailoredResultScreen } from '@/components/interview/tailored-practice/tailored-result-screen';
import PageHeader from '@/components/page-header';
import { authOptions } from '@/lib/auth';
import { getCompetitiveInterviewsResult } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const tenantId = cookies()?.get('aceprepTenantId')?.value;
  const id = props.params.id;

  const session: any = await getServerSession(authOptions);

  const currentOrg = (session?.memberships as any)?.find(
    (item) => item?.organizationId === tenantId,
  );
  let careerPractice;

  if (session?.memberships?.length > 0 && currentOrg?.role !== 'STUDENT') {
    careerPractice = await getCompetitiveInterviewsResult({
      id: id,
      sub: userId,
      isAdmin: true,
    });
  } else {
    careerPractice = await getCompetitiveInterviewsResult({
      id: id,
      sub: userId || 'Visitor',
      isAdmin: false,
    });
  }

  if (careerPractice?.error) {
    return <NotFound />;
  }

  return (
    <>
      <PageHeader hasBack title={careerPractice?.role} />
      <div
        className={`mx-auto mt-2 flex w-full ${
          !careerPractice?.result?.feedback ? '' : 'max-w-[800px]'
        } flex-col gap-4`}
      >
        <TailoredResultScreen careerPractice={careerPractice} session={session} />
      </div>
    </>
  );
}
