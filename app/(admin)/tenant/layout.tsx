'use client';

import { usePathname, useSearchParams } from 'next/navigation';

import {
  institutionMenuContents,
  institutionMenuData,
} from '@/constants/tenant-sidebar/institution';
import {
  organizationMenuContents,
  organizationMenuData,
} from '@/constants/tenant-sidebar/organization';
import { useMembershipStore } from '@/packages/shared-store';
import { SubLayoutWrapper } from '@camped-layout/sub-base';

import { Card } from '@camped-ui/card';

export default function PageLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname() ?? '';
  const searchParams = useSearchParams();
  const key = searchParams?.get('key');

  const membership = useMembershipStore((state) => (state as any).membership);
  const isInstitution = membership?.organization?.type === 'institution';
  const isSuperUser = ['admin', 'owner'].includes(membership?.role?.toLowerCase());
  const menuContents = isInstitution
    ? institutionMenuContents(isSuperUser)
    : organizationMenuContents;
  const path = menuContents?.find((item) => item?.active?.includes(pathname));

  const selectedItem = isInstitution
    ? institutionMenuData(key)?.find((item) => item?.pathName === pathname)
    : organizationMenuData(key)?.find((item) => item?.pathName === pathname);

  return (
    <Card className="border-none shadow-none">
      <SubLayoutWrapper
        pathname={path?.href ?? ''}
        menuContents={menuContents}
        mainTitle="Organization Settings"
        mainDescription="Manage your organization settings."
        subLayoutTitle={(selectedItem as any)?.subLayoutTitle}
        subLayoutDescription={(selectedItem as any)?.subLayoutDescription}
      >
        {children}
      </SubLayoutWrapper>
    </Card>
  );
}
