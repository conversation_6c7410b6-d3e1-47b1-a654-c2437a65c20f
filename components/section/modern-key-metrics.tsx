'use client';

import { Card, CardContent } from '@camped-ui/card';

// Custom horizontal bar chart component
const CompactMetricsBar = ({ data }: { data: any[] }) => {
  return (
    <div className="space-y-2 py-2">
      {data.map((item, index) => (
        <div key={index} className="flex items-center gap-3">
          {/* Label */}
          <div className="w-20 text-right text-xs font-medium text-gray-700 dark:text-gray-300">
            {item.name}
          </div>

          {/* Bar container */}
          <div className="relative flex-1">
            <div className="h-4 rounded-full bg-gray-200 dark:bg-gray-700">
              <div
                className="flex h-4 items-center justify-end rounded-full bg-blue-500 pr-1.5 transition-all duration-500 ease-out"
                style={{ width: `${Math.max(item.value, 2)}%` }}
              >
                <span className="text-xs font-medium text-white">{item.value}</span>
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* Scale reference */}
      <div className="mt-4 flex items-center gap-3 border-t border-gray-200 pt-3 dark:border-gray-700">
        <div className="w-20"></div>
      </div>
    </div>
  );
};

interface ModernKeyMetricsProps {
  careerPractice: any;
}

export const ModernKeyMetrics = ({ careerPractice }: ModernKeyMetricsProps) => {
  const feedback = careerPractice?.feedback || {};

  // Calculate metrics from feedback - scores are stored directly as numbers
  const getMetricValue = (key: string, fallback: number = 0) => {
    const value = feedback[key];
    // Handle both direct numbers and nested score objects
    if (typeof value === 'number') return value;
    if (value && typeof value === 'object' && value.score) return value.score;
    return fallback;
  };

  // Prepare data for the horizontal bar chart
  const chartData = [
    {
      name: 'Communication',
      value: getMetricValue('communication', 0),
      fullName: 'Communication',
      description: 'Clarity & articulation',
    },
    {
      name: 'Confidence',
      value: getMetricValue('confidence', 0),
      fullName: 'Confidence',
      description: 'Self-assurance & poise',
    },
    {
      name: 'Clarity',
      value: getMetricValue('clarity', 0),
      fullName: 'Clarity',
      description: 'Clear expression',
    },
    {
      name: 'Passion',
      value: getMetricValue('passion', 0),
      fullName: 'Passion',
      description: 'Enthusiasm & drive',
    },
    {
      name: 'Impact',
      value: getMetricValue('impact', 0),
      fullName: 'Impact',
      description: 'Influence & effectiveness',
    },
    {
      name: 'Language',
      value: getMetricValue('language_proficiency', 0),
      fullName: 'Language Proficiency',
      description: 'Fluency & vocabulary',
    },
  ];

  // Filter out metrics with meaningful data (> 0)
  const validData = chartData.filter((item) => item.value > 0);
  const hasData = validData.length > 0;

  // Find areas for improvement (lowest 3 scores) only if we have data
  const sortedData = hasData ? [...validData].sort((a, b) => a.value - b.value) : [];
  const improvementAreas = sortedData.slice(0, 3).map((item) => item.fullName);

  return (
    <div className="space-y-4">
      {/* Compact Chart Display */}
      {hasData ? (
        <Card>
          <CardContent className="p-4">
            <CompactMetricsBar data={validData} />

            {/* Areas for improvement */}
            {improvementAreas.length > 0 && (
              <div className="mt-3 rounded-lg bg-orange-50 p-3 dark:bg-orange-950/20">
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-orange-600 dark:text-orange-400">
                    Areas for improvement:
                  </span>{' '}
                  {improvementAreas.join(', ')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-4">
            <div className="flex h-32 items-center justify-center">
              <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                No performance data available
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
