import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { BannerNotificationScreen } from '@/components/wrapper-screen/banner-notification-screen';
import { getOrganizationById } from '@/services/apicall';

export default async function BannerNotification(props) {
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  if (!organizationId || !userId) {
    return redirect('/404');
  }
  const organization = await getOrganizationById(organizationId, userId);
  return <BannerNotificationScreen organization={organization} />;
}
