import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MemberBulkInviteScreen } from '@/components/organization/member-bulk-invite';
import { MemberListScreen } from '@/components/organization/member-list-screen';
import { authOptions } from '@/lib/auth';
import { getAllDepartment, getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId) {
    return redirect('/404');
  }
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const isInstitution = currentOrg?.organization?.type === 'institution';
  const [department] = await Promise.all([
    getAllDepartment({
      organizationId: organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);

  return (
    <MemberBulkInviteScreen
      department={department}
      role={currentOrg?.role}
      organizationId={organizationId}
      isInstitution={isInstitution}
    />
  );
}
