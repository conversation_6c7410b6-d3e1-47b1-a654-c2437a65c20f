import { cookies } from 'next/headers';

import PerformalyticsScreen from '@/components/wrapper-screen/performalytics-screen';
import {
  getAllDepartment,
  getAllGroup,
  getAllStaff,
  getPerformanceAnalytics,
  getStudentCountByGroup,
} from '@/services/apicall';

export default async function ListEvent(props) {
  const searchParams = props?.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const [data, department, group, staffs, studentCount] = await Promise.all([
    getPerformanceAnalytics({ organizationId: tenantId, searchParams }),
    getAllDepartment({ organizationId: tenantId }),
    getAllGroup({ organizationId: tenantId }),
    getAllStaff({
      searchParams,
      organizationId: tenantId,
      role: ['MEMBER', 'ADMIN'],
    }),
    getStudentCountByGroup({
      searchParams,
      organizationId: tenantId,
    }),
  ]);

  return (
    <PerformalyticsScreen
      data={data}
      staffs={staffs?.items}
      department={department?.department}
      group={group?.group}
      studentCount={studentCount?.items}
    />
  );
}
