'use client';

import React from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { Button } from '@camped-ui/button';

import OnboardingTemplate from '../onboarding-stepper/onboarding-template';

const CombinedWelcomeScreen = ({ session, interviewDetails }) => {
  const router = useRouter();
  const params = useSearchParams();
  const id = params?.get('id');
  const userName = session?.name || session?.user?.name || '';

  const handleProceedToGuidelines = () => {
    router.push(`/interview/guidelines?id=${id}`);
  };

  return (
    <div className="flex h-full w-full flex-1 flex-col items-center justify-center gap-8">
      {/* Welcome Section */}
      <div className="flex h-full w-full flex-col items-center gap-6">
        <OnboardingTemplate
          imageSrc="/interview-flow/WelcomeScreen.png"
          title={`Hey ${userName}!`}
          description="You've been invited to the online assessment. Please complete it to proceed with the selection process."
        />

        {/* Role Information */}
        <div className="w-full max-w-md space-y-2 rounded-md bg-secondary px-8 py-4">
          <p className="text-center text-sm font-normal text-muted-foreground">
            The interview is for the position of
          </p>
          <p className="text-center text-lg font-semibold">{interviewDetails?.role}</p>
        </div>

        {/* Duration Information */}
        <div className="w-full max-w-md space-y-2 rounded-md bg-primary/5 px-8 py-4">
          <p className="text-center text-sm font-normal text-muted-foreground">Duration</p>
          <p className="text-center text-lg font-semibold text-primary">
            {interviewDetails?.timing?.duration} minutes
          </p>
        </div>

        {/* What's Next Section */}
        <div className="w-full max-w-md space-y-4 rounded-lg border bg-card p-6 shadow-sm">
          <div className="text-center">
            <h3 className="text-lg font-semibold">What is Next?</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              We will help you set up your camera, microphone, and review important guidelines to
              ensure a smooth interview experience.
            </p>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Review interview guidelines</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Capture your photo</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>Test camera & microphone</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <span>Start your interview</span>
            </div>
          </div>
        </div>

        {/* Proceed Button */}
        <Button onClick={handleProceedToGuidelines} size="lg" className="min-w-[200px]">
          Proceed to Guidelines
        </Button>
      </div>
    </div>
  );
};

export default CombinedWelcomeScreen;
