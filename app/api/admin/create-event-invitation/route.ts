import { NextResponse } from 'next/server';

import InviteCandidate from '@/emails/inviteCandidate';
import InviteForMockInterview from '@/emails/inviteForMockInterview';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { getEmailTemplateByType } from '@/services/apicall';
import { createFormattedQuestions } from '@/services/create-formatted-questions';
import { render } from '@react-email/render';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

interface UserDeatils {
  id: string;
  name: string | null;
  email: string | null;
  emailVerified: Date | null;
  image: string | null;
  createdAt: Date;
  premium: boolean;
}

interface InviteCandidate {
  userResponse: UserDeatils;
  careerResponse: any;
  emailUrl: string;
  isValid: boolean;
}

const createCareerPractice = async (
  userId: string,
  role: string,
  level: string,
  eventName: string,
  questions: string[],
  organization: any,
  eventId?: string,
  timing?: any,
  isInvite?: any,
) => {
  const conversation = createFormattedQuestions(questions);
  const nextReminderDate = new Date();
  nextReminderDate.setDate(nextReminderDate.getDate() + organization?.remainderInterval);

  const careerResponse = await db.careerPractice.create({
    data: {
      userId,
      role,
      level,
      conversation,
      event: eventName,
      eventId,
      nextReminderDate: new Date(nextReminderDate),
      timing: {
        ...timing,
        inviteTime: isInvite ? new Date() : null,
      },
    },
  });

  return careerResponse;
};

const generateEmailUrl = (
  baseUrl: string,
  userEmail: string,
  token: string,
  careerResponse: any,
) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/onboarding-interview?id=${careerResponse.id}`,
  )}`;
};

const checkUserIsCandidate = async (
  users: any,
  eventName?: string,
  questions?: string[],
  eventId?: string,
  organization?: any,
  eventDetails?: any,
  role?: any,
  linkValidity?: any,
  emailContent?: any,
  isInvite?: any,
): Promise<{
  userResponses: any[];
  invitedCandidates: any[];
  organizationMembers: any[];
  alreadyExist: any[];
}> => {
  const userResponses: any = [];
  const invitedCandidates: any[] = [];
  const organizationMembers: any[] = [];
  const alreadyExist: any[] = [];

  for (const user of users) {
    const userResponse = await db.user.findFirst({
      where: {
        email: user?.toLowerCase(),
      },
      include: {
        userProfile: { select: { resumeS3Id: true } },
      },
    });

    if (userResponse && userResponse?.id) {
      const isAdminMember = await db.membership.findMany({
        where: {
          role: { in: ['ADMIN', 'MEMBER', 'OWNER', 'STUDENT'] },
          userId: userResponse?.id,
        },
      });

      const isMember = await db.membership.findFirst({
        where: {
          organizationId: organization?.id,
          userId: userResponse?.id,
        },
      });
      if (isAdminMember?.length > 0) {
        alreadyExist.push({ user });
      } else if (isMember && isMember.role === role) {
        const careerPractice = await db.careerPractice.findFirst({
          where: {
            eventId: eventId,
            userId: userResponse.id,
          },
          include: {
            eventDetails: true,
          },
        });

        if (!careerPractice) {
          const careerResponse = await createCareerPractice(
            userResponse?.id,
            eventDetails?.role || '',
            eventDetails?.level || '',
            eventName || '',
            questions || [],
            organization,
            eventId || '',
            eventDetails?.timing,
            isInvite,
          );

          if (userResponse?.userProfile?.resumeS3Id) {
            fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/server/admin/check-resume`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                id: careerResponse?.id,
                resumeS3Id: userResponse?.userProfile?.resumeS3Id,
              }),
            });
          }

          const emailTemplateProps = {
            testUrl: `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/onboarding-interview?id=${careerResponse?.id}`,
            organization: organization?.name || '',
            isPlacement: eventDetails?.isPlacement,
            emailIdentifier: organization?.mailIdentifier,
            eventName: eventDetails?.name,
            linkValidity: eventDetails?.timing?.linkValidity,
            emailContent: emailContent?.emailContent,
          };

          const interviewMockInterview = {
            ...emailTemplateProps,
            role: eventDetails?.role,
            duration: eventDetails?.timing?.duration,
            candidateName: userResponse?.name,
            linkValidity: eventDetails?.timing?.linkValidity,
            emailContent: emailContent?.emailContent,
          };

          const inviteMail = eventDetails?.isPlacement
            ? render(InviteForMockInterview(interviewMockInterview), {
                pretty: true,
              })
            : render(InviteCandidate(emailTemplateProps), {
                pretty: true,
              });
          const subject = emailContent?.emailSubject?.replaceAll(
            '{{organization}}',
            organization?.name,
          );
          const mailSendPayload = {
            to: user,
            subject:
              subject?.replaceAll('{{eventName}}', eventName) ??
              `${organization?.name} - ${eventName}`,
            html: inviteMail,
          };

          if (organization?.mailIdentifier) {
            (mailSendPayload as any).cc = [organization?.mailIdentifier];
          }
          let mailResponse;
          if (isInvite) {
            mailResponse = await campedMailer.send(mailSendPayload);
          }

          if (mailResponse?.MessageId) {
            const nextReminderDate = new Date();
            nextReminderDate.setDate(nextReminderDate.getDate() + organization?.remainderInterval);

            await db.careerPractice.update({
              data: {
                nextReminderDate,
                timing: {
                  ...(careerResponse as any)?.timing,
                  inviteTime: new Date(),
                  createdTime: new Date(),
                },
              },
              where: {
                id: careerResponse?.id,
              },
            });
          } else {
            await db.careerPractice.update({
              data: {
                timing: {
                  ...(careerResponse as any)?.timing,
                  createdTime: new Date(),
                },
              },
              where: {
                id: careerResponse?.id,
              },
            });
          }

          userResponses.push({ user, careerPracticeId: careerResponse?.id });
        } else {
          invitedCandidates.push({ user, careerPracticeId: careerPractice?.id });
        }
      } else if (isMember && isMember.role !== role) {
        organizationMembers.push({ user });
      }
    }
  }

  return { userResponses, invitedCandidates, organizationMembers, alreadyExist };
};

export async function POST(request: any) {
  const input = await request.json();

  try {
    const { users, eventId, role, allowAddingInOrganization = true, isInvite } = input;

    const token = await getToken({ req: request });

    if (!token?.email || !eventId) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }

    const eventDetails = await db.eventDetails.findUnique({
      where: {
        id: eventId,
      },
    });
    const emailTemplate = await getEmailTemplateByType(
      eventDetails?.isPlacement ? 'invite_mockInterview' : 'invite_candidate',
      eventDetails?.organizationId,
    );
    if (!eventDetails?.organizationId && !eventDetails?.name && !eventDetails) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }

    const organization = await db.organization.findUnique({
      where: {
        id: eventDetails?.organizationId,
      },
    });

    if (!organization?.name) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }

    let candidatesList, rejectedCandidates;

    if (allowAddingInOrganization && users?.length) {
      const createCandidatesUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/server/admin/create-bulk-candidate`;
      const createCandidates = await fetch(createCandidatesUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails: users,
          organizationId: organization?.id,
          role: role,
        }),
      });
      const candidates = await createCandidates.json();
      candidatesList = candidates?.items || candidates?.createdEmails;
      rejectedCandidates = candidates?.failedEmails;
    }

    let eventQuestion;
    let eventName;
    if (eventDetails?.isAiQuestion) {
      eventQuestion = ['Welcome! Walk me through your resume'];
    } else {
      eventQuestion = eventDetails?.questions;
    }
    eventName = eventDetails?.name;

    candidatesList =
      candidatesList?.map((item) => item?.value?.email) || users?.map((item) => item?.email);

    const result = await checkUserIsCandidate(
      candidatesList,
      eventName,
      eventQuestion,
      eventId,
      organization,
      eventDetails,
      role,
      (eventDetails as any)?.timing?.linkValidity * 60 * 60 || 86400,
      emailTemplate,
      isInvite,
    );

    if (result?.invitedCandidates?.length > 0 || result?.userResponses?.length > 0) {
      return NextResponse.json({
        result: 'Event Created',
        response: result,
        status: 200,
      });
    } else if (result?.organizationMembers?.length > 0) {
      return NextResponse.json({
        message: 'You cannot add organization members as candidates',
        response: result,
        status: 200,
      });
    } else if (result?.alreadyExist?.length > 0) {
      return NextResponse.json({
        message: 'Email already exist, Try with different email',
        response: result,
        status: 200,
      });
    } else {
      return NextResponse.json({
        message:
          'Failed to invite candidate. Please add Candidate first in organization Candidate List',
        status: 400,
      });
    }
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({
      message: 'Internal Server Error',
      status: 500,
    });
  }
}

const platformName = process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? 'Flinkk - Hire' : 'AcePrep';

function text({ url, organization }: { url: string; organization: string }) {
  return `Invitation for Interview Test with ${organization} on ${platformName}\n${url}\n\n`;
}
