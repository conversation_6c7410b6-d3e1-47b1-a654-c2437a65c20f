import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import PageHeader from '@/components/page-header';
import { Icon } from '@/icons';
import { getCandidateDetails, leaderboardDetails } from '@/services/apicall';
import { Phone } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Card, CardDescription, CardTitle } from '@camped-ui/card';

export default async function DemoPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const id = props?.params?.id;

  const candidateDetails = await getCandidateDetails(id, tenantId);
  const interviewathon = await leaderboardDetails({
    organizationId: tenantId,
    fetchFirstThree: false,
    searchParams: {},
    userId: id,
  });
  const user = candidateDetails?.userDetails?.userProfile;

  return (
    <>
      <PageHeader title="Student Details" hasBack />
      <div className="items-left flex flex-col justify-center py-4">
        <Card className="h-20 w-20 rounded-md border-none bg-secondary shadow-none">
          <Avatar className="h-20 w-20 rounded-md">
            <AvatarImage
              src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${id}?date=${user?.updatedAt}`}
              alt="@camped"
            />
            <AvatarFallback>
              <Icon name="User" />
            </AvatarFallback>
          </Avatar>
        </Card>

        <div className="mt-3 flex text-lg">
          <span className="font-bold">{user?.fullName}</span>
        </div>

        <div className="flex opacity-70">
          <span>{user?.degree}</span>
          <span>-</span>
          <span>{user?.specialization}</span>
        </div>

        <CardTitle className="mt-4 text-xl">Contacts</CardTitle>

        <div className="mt-2 flex w-full items-center gap-2">
          <Icon name="Mail" className="h-4 w-4" />
          <CardDescription className="align-left flex-1">
            {candidateDetails?.userDetails?.email}
          </CardDescription>
        </div>
        <div className="mt-2 flex w-full items-center gap-2">
          <Phone className="h-4 w-4" />
          <CardDescription className="align-left flex-1">{user?.contactNo || '-'}</CardDescription>
        </div>

        <CardTitle className="mt-4 text-xl">Social links</CardTitle>
        <div className="mt-2 flex w-full gap-2">
          <a href={user?.linkedinLink} target="_blank">
            <Icon
              name="Linkedin"
              className={`h-7 w-7 ${!user?.linkedinLink ? 'opacity-50' : ''}`}
            />
          </a>
          <a href={user?.githubLink} target="_blank">
            <Icon name="Github" className={`h-7 w-7 ${!user?.githubLink ? 'opacity-50' : ''}`} />
          </a>
        </div>
      </div>
      <CardTitle className="text-xl">Stats</CardTitle>
      <Card className="mt-2 grid grid-cols-2 gap-4 border-none px-3 py-4 text-center shadow-none">
        <div className="flex flex-col items-center justify-center">
          <CardTitle>{interviewathon?.user?.rank ?? '-'}</CardTitle>
          <CardDescription>Rank</CardDescription>
        </div>
        <div className="flex flex-col items-center justify-center">
          <CardTitle>{interviewathon?.user?.totalFinalScore ?? '-'}</CardTitle>
          <CardDescription>Score</CardDescription>
        </div>
        <div className="flex flex-col items-center justify-center">
          <CardTitle>{`${interviewathon?.user?.unique_interviewathon ?? '-'}/${
            interviewathon?.user?.totalInterviewathon ?? '-'
          }`}</CardTitle>
          <CardDescription>Interviewathon</CardDescription>
        </div>
        <div className="flex flex-col items-center justify-center">
          <CardTitle>{interviewathon?.user?.total_practices ?? '-'}</CardTitle>
          <CardDescription>Practices</CardDescription>
        </div>
      </Card>
    </>
  );
}
