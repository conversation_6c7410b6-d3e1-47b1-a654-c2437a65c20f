'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@camped-ui/button';
import { VideoProgressIndicator } from '@/components/ui/video-progress-indicator';
import { useOptimizedVideoSubmission } from '@/hooks/use-optimized-video-submission';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';
import { Icon } from '@/icons';

export const OptimizedVideoDemo: React.FC = () => {
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [demoStats, setDemoStats] = useState<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const {
    isProcessing,
    currentStep,
    compressionProgress,
    uploadProgress,
    overallProgress,
    error,
    canContinue,
    submitVideo,
    abortSubmission,
    resetState,
  } = useOptimizedVideoSubmission();

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      mediaRecorderRef.current = new MediaRecorder(stream);
      const chunks: Blob[] = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        setRecordedChunks(chunks);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleOptimizedSubmission = async () => {
    if (recordedChunks.length === 0) {
      alert('Please record a video first');
      return;
    }

    const startTime = Date.now();

    const result = await submitVideo(recordedChunks, {
      organizationId: 'demo-org',
      screenName: 'Demo',
      backgroundMode: true,
      onUploadComplete: (videoId) => {
        console.log('Upload completed:', videoId);
        setDemoStats(prev => ({
          ...prev,
          uploadTime: Date.now() - startTime,
          videoId,
        }));
      },
      onFeedbackComplete: (response) => {
        console.log('Feedback completed:', response);
        setDemoStats(prev => ({
          ...prev,
          totalTime: Date.now() - startTime,
          feedbackResponse: response,
        }));
      },
      onError: (error) => {
        console.error('Submission error:', error);
      },
    });

    if (result.success) {
      console.log('Submission successful:', result);
    }
  };

  const resetDemo = () => {
    setRecordedChunks([]);
    setDemoStats(null);
    resetState();
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Icon name="Video" className="h-6 w-6" />
            <span>Optimized Video Submission Demo</span>
          </CardTitle>
          <CardDescription>
            Test the new optimized video submission with Web Worker compression, 
            chunked uploads, and background processing.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Video Recording Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">1. Record Video</h3>
            <div className="flex items-center space-x-4">
              <video
                ref={videoRef}
                autoPlay
                muted
                className="w-64 h-48 bg-gray-100 rounded-lg"
              />
              <div className="space-y-2">
                <Button
                  onClick={startRecording}
                  disabled={isRecording}
                  variant={isRecording ? "secondary" : "default"}
                >
                  <Icon name={isRecording ? "Square" : "Video"} className="h-4 w-4 mr-2" />
                  {isRecording ? 'Recording...' : 'Start Recording'}
                </Button>
                <Button
                  onClick={stopRecording}
                  disabled={!isRecording}
                  variant="outline"
                >
                  <Icon name="Square" className="h-4 w-4 mr-2" />
                  Stop Recording
                </Button>
              </div>
            </div>
            {recordedChunks.length > 0 && (
              <Badge variant="secondary">
                Video recorded ({(recordedChunks.reduce((acc, chunk) => acc + chunk.size, 0) / 1024 / 1024).toFixed(2)} MB)
              </Badge>
            )}
          </div>

          {/* Submission Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">2. Submit with Optimizations</h3>
            <div className="flex space-x-4">
              <Button
                onClick={handleOptimizedSubmission}
                disabled={recordedChunks.length === 0 || isProcessing}
                className="min-w-[200px]"
              >
                <Icon name="Upload" className="h-4 w-4 mr-2" />
                Submit Video (Optimized)
              </Button>
              <Button
                onClick={abortSubmission}
                disabled={!isProcessing}
                variant="outline"
              >
                <Icon name="X" className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                onClick={resetDemo}
                variant="ghost"
              >
                <Icon name="RotateCcw" className="h-4 w-4 mr-2" />
                Reset Demo
              </Button>
            </div>
          </div>

          {/* Progress Indicator */}
          <VideoProgressIndicator
            isProcessing={isProcessing}
            currentStep={currentStep}
            overallProgress={overallProgress}
            compressionProgress={compressionProgress}
            uploadProgress={uploadProgress}
            error={error}
            canContinue={canContinue}
            onCancel={abortSubmission}
            onRetry={handleOptimizedSubmission}
          />

          {/* Performance Stats */}
          {demoStats && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Video ID:</span>
                    <p className="text-muted-foreground">{demoStats.videoId}</p>
                  </div>
                  <div>
                    <span className="font-medium">Upload Time:</span>
                    <p className="text-muted-foreground">{demoStats.uploadTime}ms</p>
                  </div>
                  <div>
                    <span className="font-medium">Total Time:</span>
                    <p className="text-muted-foreground">{demoStats.totalTime}ms</p>
                  </div>
                  <div>
                    <span className="font-medium">Background Processing:</span>
                    <p className="text-muted-foreground">{canContinue ? 'Enabled' : 'Disabled'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Features List */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Optimization Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Icon name="Zap" className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">Web Worker Compression</span>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    FFmpeg runs in background thread, UI stays responsive
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Icon name="Upload" className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Chunked Uploads</span>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    Large files uploaded in chunks with retry logic
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Icon name="BarChart" className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Real-time Progress</span>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    Detailed progress tracking for each step
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Icon name="Play" className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Background Processing</span>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    Users can continue while video processes
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};
