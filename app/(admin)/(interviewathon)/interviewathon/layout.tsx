import { cookies } from 'next/headers';

import { InterviewLayoutScreen } from '@/components/wrapper-screen/interview/event-overview-update';
import { authOptions } from '@/lib/auth';
import { GlobalTabNav } from '@/packages/shared-global-tab-nav';
import { getEventDetailsOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Card } from '@camped-ui/card';

export default async function Layout(props) {
  const { children } = props;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const eventDetails = await getEventDetailsOverview(
    tenantId,
    true,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <Card className={`relative h-full w-full overflow-x-hidden border-none shadow-none`}>
        <div className="h-full">
          <GlobalTabNav
            screenMapping={{
              interviewathon: 'Interviewathon',
            }}
          />
          <div className="h-full overflow-scroll">
            <InterviewLayoutScreen eventDetails={eventDetails} />
            {children}
          </div>
        </div>
      </Card>
    </main>
  );
}
