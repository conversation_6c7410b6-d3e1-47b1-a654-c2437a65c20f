import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import MyFrontendInterviewCard from '@/components/cards/frontend-card';
import { authOptions } from '@/lib/auth';
import { getSavedFrontendProblem } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview() {
  const userId = cookies().get('aceprepUserId')?.value;

  if (!userId) {
    return redirect('/');
  }
  const savedCode = await getSavedFrontendProblem(undefined, userId);
  const frontendPractices = savedCode?.result?.filter((item) => item?.feedback !== null);

  if (frontendPractices?.length === 0) {
    return (
      <EdgeCaseCard
        title="Sharpen Your Frontend Skills"
        description="Take advantage of our Frontend Practice section to enhance your coding abilities and prepare effectively for technical interviews. Our tailored coding challenges are designed to align with your career goals, providing detailed feedback to help you improve and master problem-solving skills."
        ctaLabel="Start Frontend Practice Now"
        ctaHref="/frontend-practice"
      />
    );
  }
  const session = await getServerSession(authOptions);

  return (
    <>
      {frontendPractices?.map((coding, index) => (
        <MyFrontendInterviewCard interview={coding} key={index} session={session} />
      ))}
    </>
  );
}
