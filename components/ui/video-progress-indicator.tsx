import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@/icons';
import { Button } from '@camped-ui/button';
import { Progress } from '@camped-ui/progress';

interface VideoProgressIndicatorProps {
  isProcessing: boolean;
  currentStep: 'idle' | 'compressing' | 'uploading' | 'processing-feedback' | 'completed' | 'error';
  overallProgress: number;
  compressionProgress: number;
  uploadProgress: number;
  error?: string | null;
  canContinue?: boolean;
  onCancel?: () => void;
  onRetry?: () => void;
  className?: string;
}

const stepLabels = {
  idle: 'Ready to submit',
  compressing: 'Optimizing video...',
  uploading: 'Uploading video...',
  'processing-feedback': 'Analyzing with AI...',
  completed: 'Upload completed!',
  error: 'Processing failed',
};

const stepIcons = {
  idle: 'Play',
  compressing: 'Loader2',
  uploading: 'Upload',
  'processing-feedback': 'Brain',
  completed: 'CheckCircle',
  error: 'AlertCircle',
};

const stepDescriptions = {
  idle: 'Click submit to start processing your video answer',
  compressing: 'Optimizing video for upload',
  uploading: 'Uploading video securely',
  'processing-feedback': 'Finalizing submission',
  completed: 'Video submitted successfully',
  error: 'Something went wrong during processing',
};

export const VideoProgressIndicator: React.FC<VideoProgressIndicatorProps> = ({
  isProcessing,
  currentStep,
  overallProgress,
  compressionProgress,
  uploadProgress,
  error,
  canContinue = false,
  onCancel,
  onRetry,
  className = '',
}) => {
  if (currentStep === 'idle') {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`rounded-lg border bg-card p-4 shadow-sm ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <Icon
            name={stepIcons[currentStep]}
            className={`h-5 w-5 ${
              currentStep === 'compressing' || currentStep === 'uploading' || currentStep === 'processing-feedback'
                ? 'animate-spin text-blue-500'
                : currentStep === 'completed'
                ? 'text-green-500'
                : currentStep === 'error'
                ? 'text-red-500'
                : 'text-gray-500'
            }`}
          />
          <div>
            <span className="font-medium text-sm">
              {stepLabels[currentStep]}
            </span>
            <p className="text-xs text-muted-foreground">
              {stepDescriptions[currentStep]}
            </p>
          </div>
        </div>

        
      </div>

      {/* Overall Progress Bar */}
      {(isProcessing || currentStep === 'completed') && (
        <div className="mb-3">
          <Progress value={overallProgress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{Math.round(overallProgress)}% complete</span>
            {currentStep === 'compressing' && (
              <span>Compression: {compressionProgress}%</span>
            )}
            {currentStep === 'uploading' && (
              <span>Upload: {uploadProgress}%</span>
            )}
          </div>
        </div>
      )}

      {/* Step-specific Progress */}

      {/* Error State */}
      {currentStep === 'error' && (
        <div className="space-y-3">
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
            <div className="flex items-start space-x-2">
              <Icon name="AlertCircle" className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">Processing Failed</p>
                <p className="text-xs mt-1">{error || 'An error occurred during video processing'}</p>
              </div>
            </div>
          </div>
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="w-full"
            >
              <Icon name="RotateCcw" className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </div>
      )}

      {/* Success State */}
      {currentStep === 'completed' && !canContinue && (
        <div className="text-sm text-green-600 bg-green-50 p-3 rounded">
          <div className="flex items-center space-x-2">
            <Icon name="CheckCircle" className="h-4 w-4" />
            <span>Video successfully processed and analyzed!</span>
          </div>
        </div>
      )}

    

     
    </motion.div>
  );
};

// Compact version for minimal UI space
export const VideoProgressIndicatorCompact: React.FC<VideoProgressIndicatorProps> = ({
  isProcessing,
  currentStep,
  overallProgress,
  canContinue,
  onCancel,
}) => {
  if (currentStep === 'idle') {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm ${
        canContinue
          ? 'bg-green-50 text-green-700'
          : 'bg-blue-50 text-blue-700'
      }`}
    >
      <Icon
        name={stepIcons[currentStep]}
        className={`h-4 w-4 ${
          isProcessing ? 'animate-spin' : ''
        }`}
      />
      <span className="flex-1">
        {stepLabels[currentStep]} ({Math.round(overallProgress)}%)
      </span>
      {canContinue && (
        <Icon name="CheckCircle" className="h-4 w-4 text-green-500" />
      )}
      {isProcessing && onCancel && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="h-6 w-6 p-0"
        >
          <Icon name="X" className="h-3 w-3" />
        </Button>
      )}
    </motion.div>
  );
};
