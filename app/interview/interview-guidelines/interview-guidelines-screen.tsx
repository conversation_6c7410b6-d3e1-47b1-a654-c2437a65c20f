'use client';

import React, { useState } from 'react';

import { OnboardingStepper } from '@/components/onboarding-stepper';
import AntiCheatGuidelines from './anti-cheat-guidelines';
import CheckPermissions from './media-permissions';
import PhotoCapture from './photo-capture';

export const InterviewGuidelinesScreen = ({ session, interviewDetails }) => {
  const [step, setStep] = useState(() => {
    // Restore progress from localStorage
    if (typeof window !== 'undefined') {
      const savedStep = localStorage.getItem('interview-guidelines-step');
      return savedStep ? parseInt(savedStep, 10) : 0;
    }
    return 0;
  });

  const pages = [
    {
      component: <AntiCheatGuidelines onNext={() => setStep(1)} />,
      buttonName: 'Continue',
      variant: 'default',
      showButton: false,
    },
    {
      component: <PhotoCapture session={session} onNext={() => setStep(2)} />,
      buttonName: 'Continue',
      variant: 'default',
      showButton: false,
    },
    {
      component: <CheckPermissions session={session} interviewDetails={interviewDetails} />,
      buttonName: 'Get Started',
      variant: 'default',
      showButton: false,
    },
  ] as any;

  return <OnboardingStepper step={step} setStep={setStep} pages={pages} session={session} />;
};
