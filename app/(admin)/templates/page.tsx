import { cookies } from 'next/headers';

import { TemplateScreen } from '@/components/wrapper-screen/template-screen';
import { data } from '@/constants/templates';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Page() {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const isInterviewathon = currentOrg?.organization?.type?.toLowerCase() === 'institution';
  return <TemplateScreen groupedData={data} isInterviewathon={isInterviewathon} />;
}
