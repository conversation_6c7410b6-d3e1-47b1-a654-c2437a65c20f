import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { getCandidateResume } from '@/services/apicall';

import { Card } from '@camped-ui/card';

export default async function DemoPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const id = props?.params?.id;

  const candidateDetails = await getCandidateResume(id, tenantId);

  if (!candidateDetails?.resumeUrl) {
    return (
      <Card className="flex min-h-[60vh] items-center justify-center p-4">
        <p className="text-center">No Resume added yet</p>
      </Card>
    );
  }

  return (
    <iframe
      style={{
        width: '100%',
        height: '100vh',
        border: 'none',
        paddingBottom: 16,
      }}
      src={`${candidateDetails?.resumeUrl}`}
    />
  );
}
