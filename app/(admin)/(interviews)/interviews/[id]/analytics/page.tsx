import { cookies } from 'next/headers';

import { InterviewAnalysis } from '@/components/wrapper-screen/interview/interview-events-analysis';
import { getInterviewAnalysis } from '@/services/apicall';

export default async function InterViewEvent(props) {
  const organizationType = 'organization';
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const eventId = props.params.id;
  const eventDetails = await getInterviewAnalysis({
    eventId,
    organizationType,
    organizationId: tenantId,
    userId: userId,
  });

  return <InterviewAnalysis eventDetails={eventDetails} />;
}
