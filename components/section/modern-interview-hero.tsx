'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Al<PERSON>Triangle,
  CheckCircle,
  Download,
  Send,
  Shield,
  Sparkles,
  User,
  XCircle,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

import ProgressCircle from '../ui/progress-circle';

interface ModernInterviewHeroProps {
  careerPractice: any;
  userProfile?: any;
  onSendFeedback?: (data: { id: string; withLink: boolean }) => void;
  disableFeedback?: boolean;
  isLoading?: boolean;
  isPublic?: boolean;
  loading?: boolean;
  onGenerateFeedback?: (id: string) => void;
  onInviteCandidate?: (data: { id: string; isInvite: boolean }) => void;
}

export const ModernInterviewHero = ({
  careerPractice,
  userProfile,
  onSendFeedback,
  disableFeedback,
  isLoading,
  isPublic = false,
  loading = false,
  onGenerateFeedback,
  onInviteCandidate,
}: ModernInterviewHeroProps) => {
  // Fix scoring system - it's out of 100, not 10
  const overallScore = careerPractice?.feedback?.overall_score || careerPractice?.finalScore || 0;

  // Fix candidate name and email extraction
  const candidateName =
    userProfile?.userProfile?.fullName ||
    userProfile?.name ||
    careerPractice?.user?.userProfile?.fullName ||
    careerPractice?.user?.name ||
    'Candidate';
  const candidateEmail = userProfile?.email || careerPractice?.user?.email || '';

  // Get user ID for profile image
  const userId =
    userProfile?.userId ||
    userProfile?.id ||
    careerPractice?.user?.id ||
    careerPractice?.userId ||
    '';

  // Get updated date for cache busting
  const updatedAt =
    userProfile?.updatedAt ||
    userProfile?.userProfile?.updatedAt ||
    careerPractice?.user?.updatedAt ||
    new Date().toISOString();

  const completedTime = careerPractice?.timing?.completedTime;
  const startTime = careerPractice?.timing?.startTime;
  const recommendation = careerPractice?.feedback?.overall_recommendation;
  const riskLevel = careerPractice?.feedback?.candidate_legitimacy?.flag_level;

  // Calculate duration
  const getDuration = () => {
    if (startTime && completedTime) {
      const start = new Date(startTime);
      const end = new Date(completedTime);
      const diffInMinutes = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
      return `${diffInMinutes} min`;
    }
    return 'N/A';
  };

  // Get recommendation badge
  const getRecommendationBadge = () => {
    if (!recommendation) return null;

    // Handle both string and object formats
    let recommendationStr = '';
    if (typeof recommendation === 'string') {
      recommendationStr = recommendation;
    } else if (typeof recommendation === 'object' && recommendation !== null) {
      // The overall_recommendation is an object with decision and reason properties
      recommendationStr =
        recommendation.decision || recommendation.recommendation || recommendation.status || '';
    } else {
      recommendationStr = String(recommendation);
    }

    if (!recommendationStr) return null;

    // Categorize recommendations properly
    const lowerRecommendation = recommendationStr.toLowerCase();
    const isPositive = ['hire', 'strong hire'].includes(lowerRecommendation);
    const isNeutral = ['weak hire'].includes(lowerRecommendation);

    // Determine badge styling based on recommendation type
    let badgeVariant: 'default' | 'destructive' | 'secondary' = 'secondary';
    let badgeClasses = '';
    let icon: React.ReactNode = null;

    if (isPositive) {
      badgeVariant = 'default';
      badgeClasses = 'border-green-200 bg-green-100 text-green-800 hover:bg-green-200';
      icon = <CheckCircle className="h-3 w-3" />;
    } else if (isNeutral) {
      badgeVariant = 'secondary';
      badgeClasses = 'border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      icon = <AlertCircle className="h-3 w-3" />;
    } else {
      badgeVariant = 'destructive';
      badgeClasses = 'border-red-200 bg-red-100 text-red-800 hover:bg-red-200';
      icon = <XCircle className="h-3 w-3" />;
    }

    return (
      <Badge
        variant={badgeVariant}
        className={cn(
          'flex h-5 items-center gap-1.5 px-2 py-0.5 text-xs font-medium',
          badgeClasses,
        )}
      >
        {icon}
        {recommendationStr.toUpperCase()}
      </Badge>
    );
  };

  // Get risk level badge
  const getRiskBadge = () => {
    if (!riskLevel) return null;

    const getRiskColor = (level: string) => {
      switch (level.toLowerCase()) {
        case 'minimal':
        case 'low':
          return 'bg-green-100 text-green-800 border-green-200';
        case 'medium':
          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case 'high':
          return 'bg-red-100 text-red-800 border-red-200';
        default:
          return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    };

    const getRiskIcon = (level: string) => {
      switch (level.toLowerCase()) {
        case 'minimal':
        case 'low':
          return <Shield className="h-3 w-3" />;
        case 'medium':
          return <AlertTriangle className="h-3 w-3" />;
        case 'high':
          return <XCircle className="h-3 w-3" />;
        default:
          return <Shield className="h-3 w-3" />;
      }
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          'flex h-5 items-center gap-1.5 px-2 py-0.5 text-xs font-medium',
          getRiskColor(riskLevel),
        )}
      >
        {getRiskIcon(riskLevel)}
        {riskLevel.toUpperCase()} RISK
      </Badge>
    );
  };

  return (
    <Card className="relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20" />

      <CardContent className="relative p-4">
        {/* Main Content Row */}
        <div className="flex items-center justify-between gap-6">
          {/* Left - Candidate Info */}
          <div className="flex flex-1 items-center gap-4">
            <Avatar className="border-3 h-32 w-32 border-white shadow-lg">
              <AvatarImage
                src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${userId}?date=${updatedAt}`}
                alt={candidateName}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-lg font-bold text-white">
                {candidateName
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>

            <div className="min-w-0 flex-1">
              <h1 className="mb-1 text-xl font-bold text-gray-900 dark:text-white">
                {candidateName}
              </h1>
              {candidateEmail && (
                <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
                  <span className="truncate">{candidateEmail}</span>
                </p>
              )}

              {/* All badges in one row */}
              <div className="flex flex-wrap gap-1.5">
                <Badge variant="secondary" className="px-2 py-0.5 text-xs">
                  {careerPractice?.role || 'Software Engineer'}
                </Badge>
                <Badge variant="outline" className="px-2 py-0.5 text-xs">
                  {careerPractice?.level || 'Mid-Level'}
                </Badge>
              </div>
              <div className="flex flex-wrap gap-1.5">
                {getRecommendationBadge()}
                {getRiskBadge()}
              </div>
            </div>
          </div>

          {/* Right - Score and Key Metrics */}
          <div className="flex items-center gap-4">
            {/* Overall Score */}
            <div className="text-center">
              <ProgressCircle
                percent={overallScore}
                width={96}
                height={96}
                strokeWidth={8}
                textSize="md"
              />
              <p className="mt-1 text-xs font-semibold text-gray-900 dark:text-white">
                Overall Score
              </p>
            </div>
          </div>
        </div>

        {/* Bottom CTA Button Group */}
        <div className="mt-6 flex flex-wrap justify-center gap-3 border-t border-gray-200 pt-4 dark:border-gray-700">
          {/* Send Feedback Button - Primary Action */}
          {onSendFeedback && (
            <Button
              className="bg-red-600 font-semibold hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
              onClick={() => onSendFeedback({ id: careerPractice?.id, withLink: false })}
              disabled={disableFeedback || isLoading}
            >
              <Send className="mr-2 h-4 w-4" />
              Feedback
            </Button>
          )}

          {/* Resume Download Button */}
          {userProfile?.resumeUrl && (
            <Button
              variant="outline"
              onClick={() => {
                window.open(userProfile.resumeUrl, '_blank');
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Resume
            </Button>
          )}

          {/* Generate AI Feedback Button */}
          {!isPublic && !careerPractice?.timing?.feedBackGenerateTime && onGenerateFeedback && (
            <Button
              variant="outline"
              onClick={() => onGenerateFeedback(careerPractice?.id)}
              disabled={loading}
            >
              <Sparkles className="mr-2 h-4 w-4" />
              {loading ? 'Generating...' : 'AI Feedback'}
            </Button>
          )}

          {/* Invite Candidate Button */}
          {!isPublic &&
            !careerPractice?.timing?.inviteTime &&
            !careerPractice?.isPlacement &&
            onInviteCandidate && (
              <Button
                variant="outline"
                onClick={() => onInviteCandidate({ id: careerPractice?.id, isInvite: true })}
              >
                <Send className="mr-2 h-4 w-4" />
                Invite Candidate
              </Button>
            )}
        </div>
      </CardContent>
    </Card>
  );
};
