import { NextResponse } from 'next/server';

import InviteCandidate from '@/emails/inviteCandidate';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { getEmailTemplateByType } from '@/services/apicall';
import { createFormattedQuestions } from '@/services/create-formatted-questions';
import { render } from '@react-email/render';

export async function POST(request) {
  try {
    const data = await request.json();
    const tenantId = request.headers.get('tenant-id');
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    if (!data?.job_applicant || !emailRegex?.test(data?.job_applicant ?? '')) {
      return NextResponse.json({ error: 'Invalid Email' }, { status: 500 });
    }
    const eventDetails = await db.eventDetails.findFirst({
      where: {
        references_id: data?.job_opening,
        organizationId: tenantId,
        status: 'ACTIVE',
      },
      include: {
        organization: true,
      },
    });

    if (!eventDetails?.id) {
      throw new Error('Event details not found');
    }

    const careerPractice = await db.careerPractice.findFirst({
      where: {
        eventId: eventDetails?.id,
        userId: data?.user_id,
      },
    });

    if (careerPractice?.id) {
      if (!careerPractice?.references_id) {
        await db.careerPractice.update({
          data: {
            references_id: data?.id,
          },
          where: {
            id: careerPractice?.id,
          },
        });
      }
      return NextResponse.json({ error: 'Candidate already invited' }, { status: 400 });
    }

    const emailTemplate = await getEmailTemplateByType('Invite Candidate', tenantId);
    const createCP = await createCareerPractice({
      email: data?.job_applicant,
      eventDetails,
      userId: data?.user_id,
      emailTemplate,
      isSendEmail: data?.is_send_email,
      data: data,
    });

    return NextResponse.json({ data, eventDetails, createCP }, { status: 200 });
  } catch (error) {
    console.error('Error in processing request:', error);
    return NextResponse.json(
      { error: error.message || 'Request processing failed' },
      { status: 500 },
    );
  }
}

const createCareerPractice = async ({
  eventDetails,
  userId,
  email,
  emailTemplate,
  isSendEmail,
  data,
}) => {
  try {
    let eventQuestion;
    const eventName = eventDetails?.name;
    if (eventDetails?.isAiQuestion) {
      eventQuestion = ['Welcome! Walk me through your resume'];
    } else {
      eventQuestion = eventDetails?.questions;
    }
    const conversation = createFormattedQuestions(eventQuestion);

    const careerResponse = await db.careerPractice.create({
      data: {
        userId: `${userId}`,
        role: eventDetails?.role,
        level: eventDetails?.level,
        conversation,
        event: eventName,
        source: data?.source,
        eventId: `${eventDetails?.id}`,
        timing: { ...(eventDetails as any)?.timing, linkValidity: 2 },
        references_id: data?.id,
      },
    });
    if (isSendEmail) {
      const subject = emailTemplate?.emailSubject?.replaceAll(
        '{{organization}}',
        eventDetails?.organization?.name,
      );
      const mailResponse = await campedMailer.send({
        from: process.env.SEND_EMAIL_FROM || '',
        to: email,
        subject:
          subject?.replaceAll('{{eventName}}', eventName) ??
          `${eventDetails?.organization?.name} - ${eventName || ''}`,
        html: render(
          InviteCandidate({
            testUrl: `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/onboarding-interview?id=${careerResponse?.id}`,
            organization: eventDetails?.organization?.name || '',
            isPlacement: (eventDetails as any)?.isPlacement,
            eventName,
            linkValidity: (eventDetails as any)?.timing?.linkValidity || 24,
            emailContent: emailTemplate?.emailContent,
            emailIdentifier: eventDetails?.organization?.mailIdentifier,
          }),
          {
            pretty: true,
          },
        ),
      });

      if (mailResponse?.MessageId) {
        const nextReminderDate = new Date();
        nextReminderDate.setDate(
          nextReminderDate.getDate() + eventDetails?.organization?.remainderInterval,
        );
        await db.careerPractice.update({
          data: {
            nextReminderDate: new Date(nextReminderDate),
            timing: {
              ...(careerResponse as any)?.timing,
              inviteTime: new Date(),
            },
          },
          where: {
            id: careerResponse?.id,
          },
        });
        return { email: email, status: 'applied' };
      }
      return { email: email, status: 'failed' };
    }
    return { email: email, status: 'applied' };
  } catch (error) {
    console.log({ error });
    return { status: 'failed', message: 'Failed to create', email };
  }
};
