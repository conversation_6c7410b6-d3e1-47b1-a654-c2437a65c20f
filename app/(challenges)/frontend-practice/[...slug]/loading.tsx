import CodeSkeleton from '@/components/skeleton/code';

import { Card } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  return (
    <div className="flex h-full w-full flex-col gap-4 md:flex-row">
      <div className="w-full max-w-[640px]" style={{ flex: 2 }}>
        <CodeSkeleton />
      </div>
      <div style={{ flex: 1 }} className="w-full pb-4 pr-2">
        <Card className="mb-1 h-1/2 w-full">
          <Skeleton className="w-4/4 my-2 h-3" />
          <Separator />

          <Skeleton className="mt-2 h-[20px] w-1/2" />
          <Skeleton className="mt-2 h-[20px] w-1/3" />
        </Card>
        <Card className="h-1/2 w-full">
          <Skeleton className="w-4/4 my-2 h-3" />
          <Separator />
        </Card>
      </div>
    </div>
  );
}
