import { cookies } from 'next/headers';

import { TeamsWebhookTable } from '@/components/data-table/teams/data-table';
import { getTeamsWebhooks } from '@/services/apicall';

export default async function Integration(props) {
  const searchParams = props.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const webhooks = await getTeamsWebhooks({ organizationId: tenantId, searchParams });

  return (
    <TeamsWebhookTable
      tasksPromise={{ data: webhooks?.integration, pageCount: webhooks?.pageCount }}
    />
  );
}
