import React from 'react';

import { cookies } from 'next/headers';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import PageHeader from '@/components/page-header';
import { CodingResultScreen } from '@/components/wrapper-screen/coding-result-screen';
import { AppLogo } from '@/layout/logo';
import { authOptions } from '@/lib/auth';
import { getMyCodingPractice } from '@/services/apicall';
import base64 from 'base-64';
import { getServerSession } from 'next-auth';

import { Button } from '@camped-ui/button';

export default async function InterViewResults(props) {
  const id = props.searchParams.id;

  if (!id) {
    redirect('/my-practices/coding-practices');
  }
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies()?.get('aceprepUserId')?.value;

  const session: any = await getServerSession(authOptions);
  let codingResult;
  const currentOrg = (session?.memberships as any)?.find(
    (item) => item?.organizationId === tenantId,
  );

  if (session?.memberships?.length > 0 && currentOrg?.role !== 'STUDENT') {
    codingResult = await getMyCodingPractice(null, id);
  } else {
    codingResult = await getMyCodingPractice(userId, id);
  }

  if (codingResult?.error || codingResult === null) {
    return (
      <>
        <div className="mx-auto mt-[60px] flex max-w-lg flex-col items-center text-center">
          <AppLogo />
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            We searched high and low, but couldn’t find what you’re looking for. Let’s find a better
            place for you to go.
          </p>

          <div className="mt-6 flex w-full shrink-0 items-center gap-x-3 sm:w-auto">
            <Link href={'/my-practices/coding-practices'}>
              <Button>Take me back</Button>
            </Link>
          </div>
        </div>
      </>
    );
  }

  const sourceCode = base64.decode(codingResult?.code);

  return (
    <div className="flex flex-col gap-4 p-4">
      <PageHeader hasBack title={codingResult?.codingProblems?.title} />
      <CodingResultScreen
        sourceCode={sourceCode}
        codingResult={codingResult}
        userId={userId}
        session={session}
      />
    </div>
  );
}
