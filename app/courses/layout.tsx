export default function Layout({ children }) {
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden`}
        style={{ paddingBottom: 88 }}
      >
        <div className="flex w-full flex-col md:flex-row">
          <div className="flex min-h-[60vh] w-full flex-col items-center justify-center px-4 pb-8 pt-2 text-center md:px-0 md:py-2">
            <div className="flex h-full w-full max-w-[980px] flex-col px-[0px] pt-[90px] md:px-[100px]">
              {children}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
