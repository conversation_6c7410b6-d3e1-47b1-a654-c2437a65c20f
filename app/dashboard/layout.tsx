import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import PageHeader from '@/components/page-header';
import { Icon } from '@/icons';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

import { Tooltip, TooltipContent, TooltipTrigger } from '@camped-ui/tooltip';

export default async function Layout({
  activity,
  insight,
  myinterview,
  challenges,
  bannerNotification,
}) {
  const session: any = await getServerSession(authOptions);
  const studentMembership = (session?.memberships as any)?.filter(
    (item) => item?.role === 'STUDENT',
  );
  if ((session?.memberships as any)?.length > 0 && studentMembership?.length === 0) {
    redirect('/home');
  }

  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden`}
        style={{ paddingBottom: 88 }}
      >
        {bannerNotification}
        {activity}
        <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">Insights</h3>
        <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">{insight}</div>
        {/* {studentMembership?.length > 0 && (
          <>
            <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">
              Problem of the Day
            </h3>
            <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {challenges}
            </div>
          </>
        )} */}
        {myinterview}
      </div>
    </main>
  );
}
