import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { VideoInterviewDetailScreen } from '@/components/wrapper-screen/interview/video-interview-detail-screen';
import { getMeetingById } from '@/services/apicall';

export default async function DemoPage(props) {
  const id = props.params.id;
  const userId = cookies().get('aceprepUserId')?.value;

  const [meeting] = await Promise.all([getMeetingById({ id })]);
  if (!meeting) {
    return <NotFound />;
  }
  return <VideoInterviewDetailScreen meeting={meeting} userId={userId} />;
}
