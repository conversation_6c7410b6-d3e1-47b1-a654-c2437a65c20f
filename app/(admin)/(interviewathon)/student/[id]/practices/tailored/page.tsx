import React from 'react';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { TailoredResultCard } from '@/components/cards/tailored-practice-result';
import { authOptions } from '@/lib/auth';
import { getUserCareerPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview(props) {
  const id = props.params.id;

  const interviews = await getUserCareerPractice(id);
  const finalData = interviews?.items?.filter((item) => item?.feedback !== null) || [];

  if (finalData?.length === 0) {
    return (
      <EdgeCaseCard
        title="No Tailored Practices"
        description="The student has not begun practicing yet."
      />
    );
  }

  const session = await getServerSession(authOptions);
  return (
    <div className="grid w-full grid-cols-1 gap-4">
      {finalData?.map((interview, index) => (
        <TailoredResultCard
          interview={interview}
          screen={'tailored-practice'}
          key={index}
          session={session}
        />
      ))}
    </div>
  );
}
