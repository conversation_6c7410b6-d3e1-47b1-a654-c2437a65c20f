import React from 'react';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { TailoredResultCard } from '@/components/cards/tailored-practice-result';
import { authOptions } from '@/lib/auth';
import { getUserCareerPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview() {
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/');
  }
  const interviews = await getUserCareerPractice(userId);

  if (interviews?.items?.length === 0) {
    return (
      <EdgeCaseCard
        title="Unleash Your Interview Potential"
        description="Supercharge your interview skills with personalized Role-Specific Interview Practice tailored to match the demands of your dream role. Benefit from expert guidance through industry-specific interview simulations and track your progress towards career advancement."
        ctaLabel="Start Tailored Practice Now"
        ctaHref="/tailored-practice"
      />
    );
  }
  const session = await getServerSession(authOptions);

  return (
    <>
      {interviews?.items?.map((interview, index) => (
        <TailoredResultCard
          interview={interview}
          screen={'tailored-practice'}
          key={index}
          session={session}
        />
      ))}
    </>
  );
}
