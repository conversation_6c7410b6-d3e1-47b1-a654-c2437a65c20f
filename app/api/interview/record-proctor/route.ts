import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data, id } = await request.json();

  try {
    let feedbacks = await db.careerPractice.update({
      where: { id },
      data: { proctorWarnings: data ?? {} },
    });
    return NextResponse.json({
      feedbacks,
      status: 200,
    });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Interview Not Found', status: 500 });
  }
}
