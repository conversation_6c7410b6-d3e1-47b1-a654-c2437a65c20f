interface ChunkUploadOptions {
  file: Blob;
  uploadUrl: string;
  chunkSize?: number;
  onProgress?: (progress: number) => void;
  onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
  maxRetries?: number;
}

interface UploadResult {
  success: boolean;
  error?: string;
  uploadTime: number;
}

const DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024; // 5MB chunks
const DEFAULT_MAX_RETRIES = 3;

export class ChunkedUploader {
  private file: Blob;
  private uploadUrl: string;
  private chunkSize: number;
  private onProgress?: (progress: number) => void;
  private onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
  private maxRetries: number;
  private abortController: AbortController;

  constructor(options: ChunkUploadOptions) {
    this.file = options.file;
    this.uploadUrl = options.uploadUrl;
    this.chunkSize = options.chunkSize || DEFAULT_CHUNK_SIZE;
    this.onProgress = options.onProgress;
    this.onChunkComplete = options.onChunkComplete;
    this.maxRetries = options.maxRetries || DEFAULT_MAX_RETRIES;
    this.abortController = new AbortController();
  }

  async upload(): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      // For small files, use direct upload
      if (this.file.size <= this.chunkSize) {
        return await this.directUpload();
      }

      // For large files, use chunked upload
      return await this.chunkedUpload();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        uploadTime: Date.now() - startTime,
      };
    }
  }

  private async directUpload(): Promise<UploadResult> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          this.onProgress?.(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve({
            success: true,
            uploadTime: Date.now() - startTime,
          });
        } else {
          reject(new Error(`Upload failed with status: ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed due to network error'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload was aborted'));
      });

      // Handle abort signal
      this.abortController.signal.addEventListener('abort', () => {
        xhr.abort();
      });

      xhr.open('PUT', this.uploadUrl);
      xhr.setRequestHeader('Content-Type', 'video/mp4');
      xhr.send(this.file);
    });
  }

  private async chunkedUpload(): Promise<UploadResult> {
    const startTime = Date.now();
    const totalChunks = Math.ceil(this.file.size / this.chunkSize);
    let uploadedBytes = 0;

    try {
      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * this.chunkSize;
        const end = Math.min(start + this.chunkSize, this.file.size);
        const chunk = this.file.slice(start, end);

        await this.uploadChunkWithRetry(chunk, chunkIndex, totalChunks);

        uploadedBytes += chunk.size;
        const progress = Math.round((uploadedBytes / this.file.size) * 100);
        this.onProgress?.(progress);
        this.onChunkComplete?.(chunkIndex, totalChunks);
      }

      return {
        success: true,
        uploadTime: Date.now() - startTime,
      };
    } catch (error) {
      throw error;
    }
  }

  private async uploadChunkWithRetry(
    chunk: Blob,
    chunkIndex: number,
    totalChunks: number
  ): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        await this.uploadChunk(chunk, chunkIndex, totalChunks);
        return; // Success, exit retry loop
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.maxRetries - 1) {
          // Wait before retrying (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  private async uploadChunk(
    chunk: Blob,
    chunkIndex: number,
    totalChunks: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Chunk upload failed with status: ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Chunk upload failed due to network error'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Chunk upload was aborted'));
      });

      // Handle abort signal
      this.abortController.signal.addEventListener('abort', () => {
        xhr.abort();
      });

      const start = chunkIndex * this.chunkSize;
      const end = start + chunk.size - 1;

      xhr.open('PUT', this.uploadUrl);
      xhr.setRequestHeader('Content-Type', 'video/mp4');
      xhr.setRequestHeader('Content-Range', `bytes ${start}-${end}/${this.file.size}`);
      xhr.setRequestHeader('X-Chunk-Index', chunkIndex.toString());
      xhr.setRequestHeader('X-Total-Chunks', totalChunks.toString());
      xhr.send(chunk);
    });
  }

  abort(): void {
    this.abortController.abort();
  }
}

// Utility function for easy chunked upload
export const uploadVideoInChunks = async (
  file: Blob,
  uploadUrl: string,
  options?: Partial<ChunkUploadOptions>
): Promise<UploadResult> => {
  const uploader = new ChunkedUploader({
    file,
    uploadUrl,
    ...options,
  });

  return uploader.upload();
};
