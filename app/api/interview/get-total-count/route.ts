import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    const eventCounts = await db.eventDetails.count({
      where: {
        organizationId: organizationId ?? '',
        isPlacement: false,
      },
    });

    return NextResponse.json({ eventCounts }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch leadership data',
      },
      { status: 500 },
    );
  }
}
