import { headers } from 'next/headers';
import Link from 'next/link';

import PageHeader from '@/components/page-header';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { Button } from '@camped-ui/button';

export default async function Layout({ children }) {
  const pathname = (await headers().get('x-next-pathname')) as string;
  const tab = pathname?.split('/')?.[2];
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-scroll`}>
      <div className={`relative h-full w-full`}>
        <div className="flex w-full items-center justify-between gap-2 overflow-x-hidden">
          <Tabs defaultValue={tab ?? 'video'}>
            <TabsList className="bg-gray-200">
              <Link href={'/question-bank/video'}>
                <TabsTrigger value="video">Video</TabsTrigger>
              </Link>
              <Link href={'/question-bank/mcq'}>
                <TabsTrigger value="mcq">MCQ</TabsTrigger>
              </Link>
            </TabsList>
          </Tabs>
          <Link href={`/question-bank/${tab}/add`}>
            <Button size="sm">Add Question</Button>
          </Link>
        </div>
        {children}
      </div>
    </main>
  );
}
