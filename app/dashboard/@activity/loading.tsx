import { Card } from '@camped-ui/card';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  const row = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 8, 9, 10, 11, 12];
  const col = [1, 2, 3, 4, 5, 6];
  const ActivityBox = ({ title }) => {
    return (
      <Card className="grid h-[150px] w-[192px] basis-1/2 content-between border-none">
        <Skeleton className="text-md mx-auto mt-3 h-[20px] w-[122px] items-center"></Skeleton>
      </Card>
    );
  };
  return (
    <div className="border-custom-border-200 grid grid-cols-1 rounded-[10px] border bg-background lg:grid-cols-3">
      <div className="divide-custom-border-200 border-custom-border-200 grid grid-cols-1 divide-y border-b lg:border-b-0 lg:border-r">
        <div className="flex">
          <ActivityBox title="Your Rank" />
          <ActivityBox title="Interviewathon" />
        </div>
        <div className="flex">
          <ActivityBox title="Practices" />
        </div>
      </div>
      <div className="p-4 lg:col-span-2">
        <h3 className="mb-2 flex items-center gap-2 font-semibold capitalize">Activity Graph</h3>
        <div className="pl-10 pt-10">
          {col.map((column, ind) => (
            <div className="flex" key={ind}>
              {row.map((row, index) => (
                <Skeleton key={index} className="m-1 flex h-4 w-4" />
              ))}
            </div>
          ))}
        </div>
        <div className="mt-8 flex items-center gap-2 text-xs">
          <span>Less</span>
          <span className="h-4 w-4 rounded bg-secondary" />
          <span className="h-4 w-4 rounded bg-primary opacity-20" />
          <span className="h-4 w-4 rounded bg-primary opacity-40" />
          <span className="h-4 w-4 rounded bg-primary opacity-80" />
          <span className="h-4 w-4 rounded bg-primary" />
          <span>More</span>
        </div>
      </div>
    </div>
  );
}
