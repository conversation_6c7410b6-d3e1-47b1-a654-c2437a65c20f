import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MyInterviewTable } from '@/components/data-table/my-interview/data-table';
import { MyInterviewsScreen } from '@/components/my-interviews/my-interviews-screen';
import { authOptions } from '@/lib/auth';
import { getMyInterview, getStaffVideoCall, getStudentVideoCall } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Card } from '@camped-ui/card';

export default async function App(props) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const searchParams = props.searchParams;
  if (!userId) {
    return redirect('/');
  }
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  if (!currentOrg?.role) {
    const myInterviews = (await getMyInterview(userId)) ?? [];
    const videoCall = (await getStudentVideoCall({ userId })) ?? [];

    return <MyInterviewsScreen myInterviews={myInterviews} videoCall={videoCall?.videoCall} />;
  }

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const videoCall =
    (await getStaffVideoCall({ userId, isSuperUser, organizationId: tenantId, searchParams }))
      ?.videoCall ?? [];

  return (
    <Card>
      <MyInterviewTable
        tasksPromise={{ totalPages: videoCall?.totalPages, items: videoCall }}
        isSuperUser={isSuperUser}
        meetingStatus={searchParams?.meetingStatus ?? 'pending'}
      />
    </Card>
  );
}
