import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { TailoredResultCard } from '@/components/cards/tailored-practice-result';
import { authOptions } from '@/lib/auth';
import { getUserCompetitiveInterviews } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview({}) {
  const userId = cookies().get('aceprepUserId')?.value;

  if (!userId) {
    return redirect('/');
  }

  const interviews = await getUserCompetitiveInterviews(userId);

  if (interviews?.items?.length === 0) {
    return (
      <EdgeCaseCard
        title="Start Your Interview Practice Journey"
        description="Enhance your interview skills and boost your confidence with tailored interview simulations designed to align with your career goals. Receive detailed feedback to track your progress and identify areas for improvement. Don't miss this opportunity to prepare effectively for success!"
        ctaLabel="Start Interview Practice Now"
        ctaHref="/competitive-interviews"
      />
    );
  }

  const session = await getServerSession(authOptions);
  return (
    <>
      {interviews?.items?.map((interview, index) => (
        <TailoredResultCard
          interview={interview}
          screen="competitive-interviews"
          key={index}
          session={session}
        />
      ))}
    </>
  );
}
