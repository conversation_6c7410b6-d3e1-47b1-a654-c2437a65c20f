import { cookies } from 'next/headers';

import { Questions } from '@/components/Modal/interview-events-question';
import { getInterviewQuestions } from '@/services/apicall';

export default async function InterViewEvent(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const eventId = props.params.id;

  const eventDetails = await getInterviewQuestions({
    eventId,
    organizationId: tenantId,
    userId: userId,
  });

  return (
    <Questions
      eventDetails={eventDetails}
      show={true}
      setShow={undefined}
      screen={eventDetails?.isPlacement ? 'interviewathon' : 'interview'}
    />
  );
}
