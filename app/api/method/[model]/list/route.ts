// pages/api/<model>/findMany.ts

import { NextResponse } from 'next/server';
import { db } from '@/prisma/db';

interface RequestParams {
  params: {
    model: string;
  };
}

export async function GET(request: Request, { params }: RequestParams) {
  const model = params?.model;
  const tenantId = request.headers.get('tenant-id');

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Unauthorized: Tenant ID is missing.' },
      { status: 401 }
    );
  }

  if (!model || !db[model]) {
    return NextResponse.json(
      { error: `Model "${model}" does not exist or is invalid.` },
      { status: 404 }
    );
  }

  try {
    const url = new URL(request.url);
    const fields = url.searchParams.get('fields'); // For field selection

    // Parse fields for selective projection
    const select = fields
      ? fields.split(',').reduce((acc, field) => {
          acc[field.trim()] = true;
          return acc;
        }, {} as Record<string, boolean>)
      : undefined;

    // Perform `findMany` operation
    const manyResponse = await db[model].findMany({
      where: {
        organizationId: tenantId,
      },
      select,
    });

    return NextResponse.json(
      {
        data: manyResponse,
        status: 200,
        message: 'Data retrieved successfully.',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Database operation failed:', error);
    return NextResponse.json(
      { error: 'Internal Server Error: Failed to fetch data.' },
      { status: 500 }
    );
  }
}
