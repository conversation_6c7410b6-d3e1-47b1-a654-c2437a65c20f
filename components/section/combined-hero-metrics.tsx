'use client';

import React from 'react';

import { ModernInterviewHero } from './modern-interview-hero';
import { ModernKeyMetrics } from './modern-key-metrics';

interface CombinedHeroMetricsProps {
  careerPractice: any;
  userProfile?: any;
  onSendFeedback?: (data: { id: string; withLink: boolean }) => void;
  disableFeedback?: boolean;
  isLoading?: boolean;
  isPublic?: boolean;
  loading?: boolean;
  onGenerateFeedback?: (id: string) => void;
  onInviteCandidate?: (data: { id: string; isInvite: boolean }) => void;
}

export const CombinedHeroMetrics = ({
  careerPractice,
  userProfile,
  onSendFeedback,
  disableFeedback,
  isLoading,
  isPublic = false,
  loading = false,
  onGenerateFeedback,
  onInviteCandidate,
}: CombinedHeroMetricsProps) => {
  // Check if we have performance metrics data
  const hasMetricsData = careerPractice?.feedback?.overall_score || careerPractice?.feedback?.overall_score === 0;

  return (
    <div className="flex flex-col gap-6 lg:flex-row lg:gap-8">
      {/* Hero Section - 60% width on large screens */}
      <div className="flex-1 lg:w-[60%]">
        <ModernInterviewHero
          careerPractice={careerPractice}
          userProfile={userProfile}
          onSendFeedback={onSendFeedback}
          disableFeedback={disableFeedback}
          isLoading={isLoading}
          isPublic={isPublic}
          loading={loading}
          onGenerateFeedback={onGenerateFeedback}
          onInviteCandidate={onInviteCandidate}
        />
      </div>

      {/* Performance Metrics - 40% width on large screens */}
      {hasMetricsData && (
        <div className="flex-1 lg:w-[40%]">
          <ModernKeyMetrics careerPractice={careerPractice} />
        </div>
      )}
    </div>
  );
};
