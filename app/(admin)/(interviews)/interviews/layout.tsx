import { cookies } from 'next/headers';

import { InterviewLayoutScreen } from '@/components/wrapper-screen/interview/event-overview-update';
import { authOptions } from '@/lib/auth';
import { GlobalTabNav } from '@/packages/shared-global-tab-nav';
import { getEventDetailsOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Layout({ children }) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const eventDetails = await getEventDetailsOverview(
    tenantId,
    false,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <div className="h-full">
      <GlobalTabNav
        screenMapping={{
          interviews: 'Interviews',
        }}
      />
      <div className="h-full overflow-scroll">
        <InterviewLayoutScreen eventDetails={eventDetails} />
        {children}
      </div>
    </div>
  );
}
