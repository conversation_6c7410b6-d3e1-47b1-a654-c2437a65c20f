import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

interface RequestParams {
  params: {
    model: string;
  };
}

export async function POST(request: Request, { params }: RequestParams) {
  const model = params?.model;
  const tenantId = request.headers.get('tenant-id');
  const input = await request.json();

  if (!tenantId) {
    return NextResponse.json({ error: 'Unauthorized: Tenant ID is missing.' }, { status: 401 });
  }

  if (!model || !db[model]) {
    return NextResponse.json(
      { error: `Model "${model}" does not exist or is invalid.` },
      { status: 404 },
    );
  }

  try {
    const uniqueResponse = await db[model].findFirst({
      where: input,
    });

    if (!uniqueResponse) {
      return NextResponse.json({ error: `No record found` }, { status: 404 });
    }

    return NextResponse.json(
      {
        data: uniqueResponse,
        message: 'Record retrieved successfully.',
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Database operation failed:', error);
    return NextResponse.json(
      { error: 'Internal Server Error: Failed to fetch data.' },
      { status: 500 },
    );
  }
}


export async function GET(request: Request, { params }: RequestParams) {
  const model = params?.model;
  const url = new URL(request.url);
  const uniqueId = url.searchParams.get('id'); // For `findUnique` operation

  if (!model || !db[model]) {
    return NextResponse.json(
      { error: `Model "${model}" does not exist or is invalid.` },
      { status: 404 },
    );
  }

  if (!uniqueId) {
    return NextResponse.json(
      { error: 'Unique ID is required for this operation.' },
      { status: 400 },
    );
  }

  try {
    const uniqueResponse = await db[model].findUnique({
      where: {
        id: uniqueId,
      },
    });

    if (!uniqueResponse) {
      return NextResponse.json(
        { error: `No record found with id "${uniqueId}".` },
        { status: 404 },
      );
    }

    return NextResponse.json(
      {
        data: uniqueResponse,
        message: 'Record retrieved successfully.',
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Database operation failed:', error);
    return NextResponse.json(
      { error: 'Internal Server Error: Failed to fetch data.' },
      { status: 500 },
    );
  }
}
