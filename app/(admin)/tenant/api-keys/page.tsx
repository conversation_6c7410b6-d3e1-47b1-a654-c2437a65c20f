import { cookies } from 'next/headers';

import { APIKeyTable } from '@/components/data-table/api-key-tabel/data-table';
import { getAllApiKeys } from '@/services/apicall';

export default async function ApiKeys(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const searchParams = props.searchParams;

  let apiKeys;
  if (tenantId) {
    apiKeys = await getAllApiKeys({ organizationId: tenantId, searchParams });
  }

  return <APIKeyTable tasksPromise={{ data: apiKeys?.api, pageCount: apiKeys?.pageCount }} />;
}
