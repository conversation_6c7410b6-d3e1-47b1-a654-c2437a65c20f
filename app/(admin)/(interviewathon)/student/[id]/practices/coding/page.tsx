import MyCodingInterviewCard from '@/components/cards/coding-card';
import { EdgeCaseCard } from '@/components/cards/edge-case';
import { authOptions } from '@/lib/auth';
import { getMyCodingPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview(props) {
  const userId = props.params.id;

  const codingPractices = await getMyCodingPractice(userId, null);

  if (codingPractices.length === 0) {
    return (
      <EdgeCaseCard
        title="No Coding Practices"
        description="The student has not begun practicing yet."
      />
    );
  }

  const session = await getServerSession(authOptions);
  return (
    <div className="grid w-full grid-cols-1 gap-4">
      {codingPractices?.map((coding, index) => {
        return <MyCodingInterviewCard coding={coding} key={index} session={session} />;
      })}
    </div>
  );
}
