import { NextResponse } from 'next/server';

import InterviewReminderEmail from '@/emails/interview-remainder-email';
import Audit from '@/lib/audit';
import { campedMailer } from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { render } from '@react-email/render';

export const maxDuration = 299;
export async function POST(request: any) {
  try {
    const careerPractices = await db.careerPractice.findMany({
      where: {
        interviewStatus: 'NOT_STARTED',
        nextReminderDate: {
          lte: new Date(), // Send reminders where the scheduled time is now or earlier
        },
        resultStatus: null,
        eventId: {
          not: null,
        },
        createdAt: {
          gte: new Date('2025-01-01 00:00:00.000'),
        },
      },
      include: {
        eventDetails: {
          include: {
            organization: true,
          },
        },
        user: true,
      },
    });
    console.log({ careerPractices });

    if (careerPractices?.length === 0) {
      console.log({ message: 'No Records Found' });
      Audit.logEvent({
        action: 'error sending remainder email',
        details: {
          message: 'No CP Records Found',
        },
      });
      return NextResponse.json(
        {
          error: 'No Records Found',
        },
        { status: 200 },
      );
    }

    const finalEmail = await Promise.all(
      careerPractices.map(async (cp) => {
        try {
          if (!cp?.eventDetails || !cp?.timing?.inviteTime) {
            console.log('eventDetails or inviteTime not found');
            Audit.logEvent({
              action: 'error sending remainder email',
              userId: cp?.userId,
              details: {
                message: 'eventDetails or inviteTime not found',
                cpId: cp?.id,
              },
            });
            return { status: 'rejected' };
          }
          const mail = await sendEmail({
            linkValidity: cp?.timing?.linkValidity,
            eventName: cp.eventDetails?.name,
            organization: cp.eventDetails?.organization,
            email: cp?.user?.email,
            id: cp?.id,
          });

          if (mail?.MessageId) {
            const nextReminderDate = new Date();
            nextReminderDate.setDate(
              nextReminderDate.getDate() + cp.eventDetails?.organization?.remainderInterval,
            );
            await db.careerPractice.update({
              where: { id: cp.id },
              data: {
                nextReminderDate: nextReminderDate,
              },
            });
            return { status: 'fulfilled', email: cp?.user?.email };
          } else {
            console.log({ mail });
            Audit.logEvent({
              action: 'error sending remainder email',
              userId: cp?.userId,
              details: {
                mail,
                cpId: cp?.id,
              },
            });
            return { status: 'rejected' };
          }
        } catch (error) {
          console.log('error', error);
          Audit.logEvent({
            action: 'error sending remainder email',
            userId: cp?.userId,
            details: {
              error,
              cpId: cp?.id,
            },
          });
          return { status: 'rejected' };
        }
      }),
    );

    return NextResponse.json(
      {
        rejected: finalEmail?.filter((items) => items?.status !== 'fulfilled'),
        fulfilled: finalEmail?.filter((items) => items?.status === 'fulfilled'),
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Error while fetching data',
      },
      { status: 500 },
    );
  }
}

const sendEmail = async ({ email, organization, eventName, linkValidity, id }) => {
  const mailResponse = await campedMailer.send({
    from: process.env.SEND_EMAIL_FROM || '',
    to: email,
    subject: `Reminder: Your test for ${eventName} is pending completion.`,
    html: render(
      InterviewReminderEmail({
        organization: organization?.name,
        testUrl: `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/onboarding-interview?id=${id}`,
        emailIdentifier: organization?.emailIdentifier,
        eventName,
        linkValidity,
      }),
      {
        pretty: true,
      },
    ),
  });
  return mailResponse;
};
const generateEmailUrl = (baseUrl: string, userEmail: string, token: string, id: string) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/onboarding-interview?id=${id}`,
  )}`;
};
