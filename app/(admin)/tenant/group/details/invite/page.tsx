import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { GroupMemberBulkInviteScreen } from '@/components/group/member-bulk-invite';
import {
  getAllGroup,
  getDepartmentMembershipMappingByDepartmentId,
  getStaffList,
} from '@/services/apicall';

export default async function ListEvent(props) {
  const groupId = props?.searchParams?.id;
  const groupName = props?.searchParams?.key;
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId) {
    return redirect('/404');
  }
  const [members, group] = await Promise.all([
    getStaffList({
      organizationId: organizationId,
      role: ['MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    getAllGroup({
      groupId,
    }),
  ]);
  const departmentAdminList = await getDepartmentMembershipMappingByDepartmentId({
    departmentId: group?.group?.[0]?.department?.id,
    role: ['ADMIN'],
  });
  const membersWithoutDepartmentMapping = members?.items?.filter(
    (item) =>
      !departmentAdminList?.departmentMembershipMapping?.some(
        (oldItem) => oldItem?.membershipId === item?.membershipId,
      ),
  );
  return (
    <GroupMemberBulkInviteScreen
      members={membersWithoutDepartmentMapping}
      groupName={groupName}
      groupId={groupId}
    />
  );
}
