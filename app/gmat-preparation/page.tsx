import { cookies } from 'next/headers';

import { QuizScreen } from '@/components/wrapper-screen/quiz-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function DemoPage() {
  const session = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;

  return (
    <QuizScreen
      quiz={[
        {
          id: 1,
          name: 'Verbal',
          slug: 'verbal',
          description: 'Enhance language skills, vital for interviews and aptitude tests.',
          isAuth: true,
          isPremium: false,
        },
        {
          id: 2,
          name: 'Quants',
          slug: 'quant',
          description: 'Enhance language skills, vital for interviews and aptitude tests.',
          isAuth: true,
          isPremium: false,
        },
        {
          id: 3,
          name: 'Data Insights',
          slug: 'data_insights',
          description: 'Enhance language skills, vital for interviews and aptitude tests.',
          isAuth: true,
          isPremium: false,
        },
      ]}
      company={[]}
      session={session}
      tenantId={tenantId}
    />
  );
}
