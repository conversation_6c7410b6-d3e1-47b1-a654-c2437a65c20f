import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';
import { v4 as uuid } from 'uuid';

export const maxDuration = 299;

export async function POST(request: any) {
  const token = await getToken({ req: request });
  const { role, level } = await request.json();
  const userId = token?.sub;

  if (!(role && level)) {
    return NextResponse.json(
      {
        message: 'Role is Missing',
      },
      { status: 403 },
    );
  }

  if (
    !token &&
    !['Software Engineer', 'History (Ancient & Medieval)']?.includes(role) &&
    level !== 'Fresher'
  )
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  const response = await db.careerPractice.create({
    data: {
      userId: userId || 'Visitor',
      role: role || '',
      level: level || '',
      event: 'tailored-practice',
      conversation: [
        {
          id: uuid(),
          question: `Welcome! Walk me through your resume`,
          isAnswered: false,
          startTime: new Date() as any,
          answer: '',
        },
      ],
      timing: {
        startTime: new Date().toISOString(),
      },
    },
  });

  return NextResponse.json({
    id: response.id,
    status: 200,
  });
}
