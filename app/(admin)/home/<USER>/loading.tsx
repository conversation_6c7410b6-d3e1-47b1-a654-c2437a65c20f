import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
import { SVGSkeleton, Skeleton } from '@/components/skeleton/skeleton';

export default function Loading() {
  return (
    <div className="h-[60vh] rounded-md border p-4 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="tracking-tight">
          <Skeleton className="w-[128px] max-w-full" />
        </h3>
      </div>
      <div className="mb-4 h-[44vh]">
        <div className="relative max-h-[44vh] overflow-auto">
          <DataTableSkeleton
            columnCount={3}
            cellWidths={['5rem', '5rem', '5rem']}
            shrinkZero
            showViewOptions={false}
          />
        </div>
      </div>
      <div className="mt-1 flex justify-between">
        <div className="flex items-center justify-center">
          <Skeleton className="w-[88px] max-w-full" />
        </div>
        <div className="flex items-center space-x-1">
          <div className="hidden size-5 h-10 w-10 items-center justify-center rounded-md border border-input p-0 transition-colors lg:flex">
            <SVGSkeleton className="size-4 h-[15px] w-[15px]" />
          </div>
          <div className="inline-flex size-8 h-10 w-10 items-center justify-center rounded-md border border-input transition-colors">
            <SVGSkeleton className="size-4 h-[15px] w-[15px]" />
          </div>
          <div className="inline-flex size-8 h-10 w-10 items-center justify-center rounded-md border border-input transition-colors">
            <SVGSkeleton className="size-4 h-[15px] w-[15px]" />
          </div>
          <div className="hidden size-8 h-10 w-10 items-center justify-center rounded-md border border-input transition-colors lg:flex">
            <SVGSkeleton className="size-4 h-[15px] w-[15px]" />
          </div>
        </div>
      </div>
    </div>
  );
}
