'use client';

import { useState } from 'react';

import ChatTextarea from '@/components/chat/ChatTextarea';
import Content from '@/components/chat/Content';
import Footer from '@/components/chat/Footer';

export default function ChatPage() {
  const [messages, setMessages] = useState([]);
  return (
    <div className="flex h-screen flex-col">
      <Content messages={messages} />
      <Footer>
        <ChatTextarea setMessages={setMessages} />
      </Footer>
    </div>
  );
}
