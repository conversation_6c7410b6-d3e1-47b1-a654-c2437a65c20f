import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { ViewInterviewDetailScreen } from '@/components/wrapper-screen/interview/view-interview-detail-screen';
import { authOptions } from '@/lib/auth';
import { getAllIntegration, getInterviewDetails, getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ViewInterviewDetail(props) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const id = props.params.practiceId;
  const tenantId = cookies()?.get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [response, members, platform] = await Promise.all([
    getInterviewDetails(id, currentOrg?.id, currentOrg?.role),
    getStaffList({
      organizationId: tenantId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    getAllIntegration({ organizationId: tenantId }),
  ]);
  if (!response) {
    return <NotFound />;
  }
  return (
    <ViewInterviewDetailScreen
      careerPractice={response?.result}
      userProfile={response?.userProfile}
      user={response?.user}
      userId={userId}
      members={members}
      platform={platform
        ?.filter((item) => ['GOOGLE', 'OUTLOOK']?.includes(item?.platform))?.[0]
        ?.platform?.toLowerCase()}
    />
  );
}
