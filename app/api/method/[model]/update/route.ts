import { NextResponse } from 'next/server';
import { db } from '@/prisma/db';

interface RequestParams {
  params: {
    model: string;
  };
}

export async function PUT(request: Request, { params }: RequestParams) {
  const model = params?.model;
  const tenantId = request.headers.get('tenant-id');
  const { id, ...updateData } = await request.json();

  // Validation
  if (!tenantId) {
    return NextResponse.json({ error: 'Unauthorized: Tenant ID is missing.' }, { status: 401 });
  }
  if (!model || !db[model]) {
    return NextResponse.json(
      { error: `Model "${model}" does not exist or is invalid.` },
      { status: 404 },
    );
  }
  if (!id) {
    return NextResponse.json(
      { error: 'Bad Request: Record ID is required for update.' },
      { status: 400 },
    );
  }

  try {
    // Attempt to update and handle not found error in one step
    const response = await db[model].update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(
      {
        data: response,
        message: 'Record updated successfully.',
      },
      { status: 200 },
    );
  } catch (error: any) {
    console.error('Database update operation failed:', error);

    // Handle Prisma-specific "Record not found" error
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: `Record with ID "${id}" not found.` },
        { status: 404 },
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error: Failed to update data.' },
      { status: 500 },
    );
  }
}
