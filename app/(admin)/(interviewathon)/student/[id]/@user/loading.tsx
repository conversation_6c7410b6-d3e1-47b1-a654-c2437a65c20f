import PageHeader from '@/components/page-header';

import { CardTitle } from '@camped-ui/card';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  return (
    <div className="h-full w-full">
      <div className="h-full w-full">
        <PageHeader title="Student Details" />
        <div className="items-left flex flex-col justify-center py-4">
          <Skeleton className="h-20 w-20 rounded-md" />

          <Skeleton className="mt-3 h-6 w-40" />

          <Skeleton className="mt-3 h-6 w-40" />

          <CardTitle className="mt-4 text-xl">Contacts</CardTitle>

          <div className="mt-2 flex w-full items-center gap-2">
            <Skeleton className="h-6 w-6" />
            <Skeleton className="h-6 w-40" />
          </div>
          <div className="mt-2 flex w-full items-center gap-2">
            <Skeleton className="h-6 w-6" />
            <Skeleton className="h-6 w-40" />
          </div>

          <CardTitle className="mt-4 text-xl">Social links</CardTitle>
          <div className="mt-2 flex w-full gap-2">
            <Skeleton className={`h-7 w-7`} />
            <Skeleton className={`h-7 w-7`} />
          </div>
        </div>
      </div>
    </div>
  );
}
