import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import RefreshCard from '@/components/cards/refresh-card';
import { TailoredResultScreen } from '@/components/interview/tailored-practice/tailored-result-screen';
import { authOptions } from '@/lib/auth';
import { getTailoredPracticesResult } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props.params.practiceId;
  const studentId = props.params.id;

  if (!id) {
    redirect(`/my-practices/competitive-interviews`);
  }
  const userId = cookies()?.get('aceprepUserId')?.value;

  const session: any = await getServerSession(authOptions);

  const careerPractice = await getTailoredPracticesResult({
    id: id,
    sub: userId || 'Visitor',
    isAdmin: true,
  });
  if (!careerPractice?.hasCompleted && careerPractice?.id) {
    return (
      <RefreshCard
        showRefresh={false}
        buttonTitle={'Resume Interview'}
        href={`/competitive-interviews/${careerPractice?.id}`}
        description={
          'Your interview is not yet completed. To continue, please click the Resume Interview button below.'
        }
      />
    );
  }

  return (
    <>
      <BreadCrumbWrapper
        data={[
          {
            title: 'Competitive Interviews',
          },
          {
            title: `${careerPractice?.result?.role} - ${careerPractice?.result?.level}`,
          },
        ]}
      />
      <div
        className={`mx-auto flex w-full ${
          !careerPractice?.result?.feedback ? '' : 'max-w-[800px]'
        } flex-col gap-4`}
      >
        <TailoredResultScreen careerPractice={careerPractice} session={session} />
      </div>
    </>
  );
}
