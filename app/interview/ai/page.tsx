import { redirect } from 'next/navigation';

import DailyAIInterview from '@/components/wrapper-screen/interview/daily-ai-interview';
import { authOptions } from '@/lib/auth';
import { getInterviewQuestion } from '@/services/apicall';
import axios from 'axios';
import { getServerSession } from 'next-auth';

export default async function AIInterview(props) {
  const session = await getServerSession(authOptions);
  const id = props?.searchParams?.id;
  if (!session?.userId) {
    return redirect('/sign-in');
  }
  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: session?.userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    return redirect(`/completed?id=${id}`);
  }

  if (!careerPractice?.timing?.startTime) {
    return redirect(`/onboarding-interview?id=${id}`);
  }

  if (careerPractice?.round === 'coding-interview') {
    return redirect(`/interview/coding?id=${id}`);
  }

  if (careerPractice?.round === 'frontend-interview') {
    return redirect(`/interview/frontend?id=${id}`);
  }
  if (careerPractice?.round === 'multiple-choice') {
    return redirect(`/interview/multiple-choice?id=${id}`);
  }
  const daily = await axios.get(
    `${process.env.NEXT_PUBLIC_WS_URL}/?role=${careerPractice?.role}&level=${careerPractice?.level}&id=${id}&tenantId=${careerPractice?.organizationId}`,
  );
  if (!daily.data) {
    throw new Error('Failed to fetch data');
  }
  const dailyUrl = daily.data;
  return <DailyAIInterview id={id} url={dailyUrl?.[0]} />;
}
