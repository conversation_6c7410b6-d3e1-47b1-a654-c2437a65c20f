import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { DepartmentTable } from '@/components/department/department-listing';
import { authOptions } from '@/lib/auth';
import { getAllDepartment } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  if (!organizationId) {
    return redirect('/404');
  }

  const departmentList = await getAllDepartment({
    organizationId: organizationId,
    membershipId: isSuperUser ? undefined : currentOrg?.id,
  });
  return (
    <DepartmentTable
      isSuperUser={isSuperUser}
      tasksPromise={{ data: departmentList?.department }}
    />
  );
}
