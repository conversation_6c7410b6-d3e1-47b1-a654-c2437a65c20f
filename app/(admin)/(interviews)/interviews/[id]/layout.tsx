import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import InterviewTabs from '@/components/interviews/tabs';
import { authOptions } from '@/lib/auth';
import { getInterviewList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Layout({ children, modal }) {
  const pathname = (await headers().get('x-next-pathname')) as string;
  const eventId = pathname?.split('/')?.[2];
  const screen = pathname?.split('/')?.[3];
  const session: any = await getServerSession(authOptions);

  const organizationId = cookies().get('aceprepTenantId')?.value;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};
  const userId = cookies().get('aceprepUserId')?.value;

  const interviewResult = await getInterviewList({
    id: eventId,
    organizationId,
    userId: userId,
    searchParams: {},
    membershipId: currentOrg?.id,
    role: currentOrg?.role,
  });
  if (!interviewResult?.items) return redirect('/404');
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden`}
        style={{ paddingBottom: 88 }}
      >
        <InterviewTabs eventId={eventId} title={interviewResult?.items?.name} />

        {children}
      </div>
    </main>
  );
}
