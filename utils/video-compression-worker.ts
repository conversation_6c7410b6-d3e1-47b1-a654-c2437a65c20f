// Web Worker for non-blocking video compression
import { createFFmpeg, fetchFile } from '@ffmpeg/ffmpeg';

const ffmpeg = createFFmpeg({
  corePath: 'https://unpkg.com/@ffmpeg/core-st/dist/ffmpeg-core.js',
  mainName: 'main',
  log: false,
});

interface CompressionMessage {
  type: 'compress';
  file: Blob;
  unique_id: string;
  mimeType: string;
  screenName: string;
}

interface ProgressMessage {
  type: 'progress';
  progress: number;
}

interface CompletedMessage {
  type: 'completed';
  compressedBlob: Blob;
  stats: {
    originalSize: number;
    compressedSize: number;
    compressionRatio: string;
    processingTime: number;
  };
}

interface ErrorMessage {
  type: 'error';
  error: string;
}

type WorkerMessage = ProgressMessage | CompletedMessage | ErrorMessage;

// Optimized compression settings for different file sizes
const getCompressionSettings = (fileSize: number) => {
  if (fileSize < 10000000) { // < 10MB - minimal compression
    return {
      preset: 'veryfast',
      crf: '28',
      shouldCompress: false
    };
  } else if (fileSize < 50000000) { // < 50MB - moderate compression
    return {
      preset: 'fast',
      crf: '25',
      shouldCompress: true
    };
  } else { // > 50MB - aggressive compression
    return {
      preset: 'medium',
      crf: '22',
      shouldCompress: true
    };
  }
};

self.onmessage = async (event: MessageEvent<CompressionMessage>) => {
  const { file, unique_id, mimeType, screenName } = event.data;
  
  try {
    const startTime = Date.now();
    const settings = getCompressionSettings(file.size);

    // Send initial progress
    self.postMessage({ type: 'progress', progress: 0 } as ProgressMessage);

    if (!ffmpeg.isLoaded()) {
      await ffmpeg.load();
      self.postMessage({ type: 'progress', progress: 10 } as ProgressMessage);
    }

    ffmpeg.FS('writeFile', `${unique_id}`, await fetchFile(file));
    self.postMessage({ type: 'progress', progress: 20 } as ProgressMessage);

    let compressedData;
    
    try {
      if (settings.shouldCompress) {
        // Set up progress monitoring
        ffmpeg.setProgress(({ ratio }) => {
          const progress = Math.round(20 + (ratio * 70)); // 20-90% for compression
          self.postMessage({ type: 'progress', progress } as ProgressMessage);
        });

        await ffmpeg.run(
          '-i',
          `${unique_id}`,
          '-preset',
          settings.preset,
          '-vcodec',
          'libx264',
          '-crf',
          settings.crf,
          '-movflags',
          '+faststart', // Optimize for web streaming
          '-pix_fmt',
          'yuv420p', // Ensure compatibility
          'output.mp4',
        );
        compressedData = ffmpeg.FS('readFile', 'output.mp4');
      } else {
        // Skip compression for small files
        compressedData = ffmpeg.FS('readFile', `${unique_id}`);
        self.postMessage({ type: 'progress', progress: 90 } as ProgressMessage);
      }
    } catch (compressionError) {
      console.log('Compression failed, using original file', compressionError);
      compressedData = ffmpeg.FS('readFile', `${unique_id}`);
    }

    // Create Blob from compressed video data
    const compressedBlob = new Blob([compressedData.buffer], {
      type: mimeType,
    });

    const endTime = Date.now();
    const processingTime = endTime - startTime;
    const compressionRatio = (compressedData.length / file.size).toFixed(2);

    await ffmpeg.exit();

    self.postMessage({
      type: 'completed',
      compressedBlob,
      stats: {
        originalSize: file.size,
        compressedSize: compressedData.length,
        compressionRatio,
        processingTime,
      },
    } as CompletedMessage);

  } catch (error) {
    console.error('Worker compression error:', error);
    
    // Return original file as fallback
    const fallbackBlob = new Blob([file], {
      type: mimeType,
    });

    self.postMessage({
      type: 'completed',
      compressedBlob: fallbackBlob,
      stats: {
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: '1.00',
        processingTime: 0,
      },
    } as CompletedMessage);
  }
};

export {};
