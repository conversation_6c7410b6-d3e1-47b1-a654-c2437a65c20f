import { redirect } from 'next/navigation';

import { InterviewScreen } from '@/components/interview/interview-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewQuestion, getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;

  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: session?.userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    return redirect(`/completed?id=${id}`);
  }

  if (!careerPractice?.timing?.startTime) {
    return redirect(`/onboarding-interview?id=${id}`);
  }

  if (careerPractice?.round === 'coding-interview') {
    return redirect(`/interview/coding?id=${id}`);
  }
  if (careerPractice?.round === 'video-interview' && careerPractice?.isAiQuestion) {
    return redirect(`/interview/ai?id=${id}`);
  }

  if (careerPractice?.round === 'frontend-interview') {
    return redirect(`/interview/frontend?id=${id}`);
  }
  if (careerPractice?.round === 'multiple-choice') {
    return redirect(`/interview/multiple-choice?id=${id}`);
  }

  if (careerPractice?.round === 'written-interview') {
    return redirect(`/interview/written-interview?id=${id}`);
  }

  const userProfile = await getUserProfile(session?.userId);

  return (
    <InterviewScreen
      careerPractice={careerPractice}
      practiceId={id}
      session={{ ...session, updatedAt: userProfile?.updatedAt }}
    />
  );
}
