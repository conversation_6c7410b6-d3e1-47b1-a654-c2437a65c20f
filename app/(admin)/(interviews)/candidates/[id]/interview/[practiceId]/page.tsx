import { cookies } from 'next/headers';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import { InterviewDetailScreen } from '@/components/wrapper-screen/student-details-screen/interviewathon-detail-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ViewInterviewDetail(props) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const id = props.params.practiceId;
  const tenantId = cookies()?.get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const response = await getInterviewDetails(id, currentOrg?.id, currentOrg?.role);

  return (
    <>
      <BreadCrumbWrapper
        data={[
          {
            title: 'Interview',
          },
          {
            title: response?.result?.event,
          },
        ]}
      />
      <div className="flex w-full max-w-[800px] flex-col gap-4">
        <InterviewDetailScreen careerPractice={response?.result} userId={userId} />
      </div>
    </>
  );
}
