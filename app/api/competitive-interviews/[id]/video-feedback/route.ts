import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import Audit from '@/lib/audit';
import { getUserSubscriptionPlan } from '@/pages/api/getSubscriptionStatus';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';
const folder = process.env.NEXT_PUBLIC_S3FOLDER || '';

// Function to get GCP bucket from organization or use default
async function getGCPBucket(organizationId) {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching organization GCP bucket:', error);
    return defaultBucket;
  }
}

// Function to update conversation with feedback
const updateConversation = async (
  data: any,
  transcript: string,
  questionId: string,
  s3Id: string,
  feedback: any,
) => {
  if (!data) {
    return null;
  }

  const conversation: any = data.conversation || [];

  const updatedConversation = conversation.map((item) => {
    if (item?.id === questionId) {
      return {
        ...item,
        answer: transcript,
        s3Id: s3Id,
        isAnswered: true,
        videoOrigin: s3Id ? 'GCP_BUCKET' : '',
        feedback,
        completedTime: new Date(),
      };
    }
    return item;
  });

  return updatedConversation;
};

export async function POST(request: any) {
  const { url, question, userId, level, role, organizationId, id, questionId }: any = await request.json();
  let plan = {};
  let membership, response;
  try {
    if (userId && userId !== 'Visitor') {
      membership = await db.membership.findMany({
        where: {
          user: { id: userId },
          role: { in: ['STUDENT'] },
        },
      });
      if (membership?.length === 0 && !membership?.length) {
        plan = await getUserSubscriptionPlan(userId);
      }
    }
    const hasAdvancedAnalysis =
      Boolean((plan as unknown as { plan?: string })?.plan) ||
      ['Software Engineer', 'History (Ancient & Medieval)']?.includes(role ?? '') ||
      membership?.length > 0;
    let systemPrompt;

    if (hasAdvancedAnalysis) {
      systemPrompt = await generatePrompt('COMPETITIVE_INTERVIEW_FEEDBACK_SYSTEM_PROMPT_STRATEGY', {
        role: role,
        level: level,
      });
    } else {
      systemPrompt = await generatePrompt(
        'COMPETITIVE_INTERVIEW_NON_PREMIUM_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
        {
          role: role,
          level: level,
        },
      );
    }
    // Get bucket from organization or use default
    const bucket = await getGCPBucket(organizationId);

    response = await generateVideoResult({ systemInput: systemPrompt, url, question, bucket });

    try {
      // Clean the response text by removing markdown code blocks and other potential formatting issues
      const cleanedText = response?.parts?.[0]?.text
        ?.replaceAll('```json', '')
        ?.replaceAll('```', '');
      const feedback = JSON.parse(cleanedText);

      // Save the feedback to database
      if (id && questionId) {
        const data = await db.careerPractice.findFirst({
          where: {
            id: id,
            userId: userId,
          },
        });

        const updatedConversation = await updateConversation(
          data,
          feedback.transcript || '',
          questionId,
          url,
          feedback,
        );

        if (updatedConversation) {
          await db.careerPractice.update({
            data: {
              conversation: updatedConversation,
            },
            where: {
              id: id,
            },
          });
          console.log('✅ Competitive interview feedback saved to database successfully');
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Video feedback generated and saved successfully'
      }, { status: 200 });

    } catch (jsonError) {
      console.log('JSON parsing error:', jsonError);
      Audit.logEvent({
        action: 'video_feedback_json_parse_failed',
        userId: userId,
        details: {
          screen: 'competitive interview',
          userId: userId,
          s3Id: url,
          error: jsonError,
          result: response?.parts?.[0]?.text,
        },
      });

      // Attempt to regenerate the response
      console.log('Attempting to regenerate AI response due to invalid JSON format');
      response = await generateVideoResult({ systemInput: systemPrompt, url, question, bucket });

      try {
        // Try parsing the regenerated response
        const cleanedText = response?.parts?.[0]?.text
          ?.replaceAll('```json', '')
          ?.replaceAll('```', '');
        const feedback = JSON.parse(cleanedText);

        // Save the feedback to database
        if (id && questionId) {
          const data = await db.careerPractice.findFirst({
            where: {
              id: id,
              userId: userId,
            },
          });

          const updatedConversation = await updateConversation(
            data,
            feedback.transcript || '',
            questionId,
            url,
            feedback,
          );

          if (updatedConversation) {
            await db.careerPractice.update({
              data: {
                conversation: updatedConversation,
              },
              where: {
                id: id,
              },
            });
            console.log('✅ Competitive interview feedback saved to database successfully (after retry)');
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Video feedback generated and saved successfully'
        }, { status: 200 });

      } catch (retryError) {
        console.log('Retry JSON parsing error:', retryError);
        return NextResponse.json(
          { error: 'Failed to parse AI response after retry' },
          { status: 500 },
        );
      }
    }
  } catch (error) {
    console.log('competitive video feedback error ', { error });
    Audit.logEvent({
      action: 'video_feedback_generation_failed',
      userId: userId,
      details: {
        screen: 'competitive interview',
        userId: userId,
        s3Id: url,
        error,
        result: response?.parts?.[0]?.text,
      },
    });

    return NextResponse.json({ error: 'competitive video feedback error ' }, { status: 500 });
  }
}

const generateVideoResult = async ({ systemInput, url, question, bucket }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.5,
      topP: 0.5,
    },
  };

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: `gs://${bucketName}/${folder}/${url}`,
      },
    },
    question,
  });

  return stream;
};
