import { cookies } from 'next/headers';

import { EditEmailTemplates } from '@/components/email-templates/edit-email-template';
import { emailTemplates, hireEmailTemplates } from '@/constants/email-templates';
import { getEmailTemplateByType } from '@/services/apicall';

export default async function EmailTemplates(props) {
  const key = props?.searchParams?.key;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const EmailTemplate = await getEmailTemplateByType(key, tenantId);
  const templateData: any =
    process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? hireEmailTemplates : emailTemplates;

  return (
    <EditEmailTemplates
      defaultTemplate={templateData?.find((item) => item.emailType === key)}
      EmailTemplate={EmailTemplate ?? templateData?.find((item) => item.emailType === key)}
    />
  );
}
