import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import { TailoredResultScreen } from '@/components/interview/tailored-practice/tailored-result-screen';
import { authOptions } from '@/lib/auth';
import { getTailoredPracticesResult } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props.params.practiceId;
  const studentId = props.params.id;

  if (!id) {
    redirect(`/student/${studentId}/practices/tailored`);
  }
  const userId = cookies()?.get('aceprepUserId')?.value;

  const session: any = await getServerSession(authOptions);

  const careerPractice = await getTailoredPracticesResult({
    id: id,
    sub: userId || 'Visitor',
    isAdmin: true,
  });

  return (
    <>
      <BreadCrumbWrapper
        data={[
          {
            title: 'Tailored Practice',
          },
          {
            title: `${careerPractice?.result?.role} - ${careerPractice?.result?.level}`,
          },
        ]}
      />
      <div className="mx-auto flex w-full max-w-[800px] flex-col gap-4">
        <TailoredResultScreen careerPractice={careerPractice} session={session} />
      </div>
    </>
  );
}
