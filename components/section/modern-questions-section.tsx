'use client';

import { useState } from 'react';

import { Icon } from '@/icons';
import {
  SandpackCodeEditor,
  SandpackFileExplorer,
  SandpackLayout,
  SandpackPreview,
  SandpackProvider,
} from '@codesandbox/sandpack-react';
import base64 from 'base-64';
import {
  CheckCircle,
  Code,
  FileText,
  MessageSquare,
  Play,
  Target,
  Video,
  XCircle,
} from 'lucide-react';
import { useTheme } from 'next-themes';
import ReactMarkdown from 'react-markdown';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

import { InterviewQuestionTiming } from '../cards/admin/interview-question-timing';
import { CodingResultStatusCard } from '../cards/coding-result-status-card';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '../ui/resizeable';
import { VideoPlayer } from '../video/player';
import { FeedBackSection, PerformanceScoreSection } from './index';

interface ModernQuestionsSectionProps {
  questions: any[];
  careerPractice?: any;
  userId?: string;
}

// Helper function to get question type icon and color
const getQuestionTypeInfo = (round: string) => {
  const typeMap = {
    'video-interview': {
      icon: Video,
      color: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
      borderColor: 'border-purple-200 dark:border-purple-800',
      label: 'Video Interview',
    },
    'coding-interview': {
      icon: Code,
      color: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      label: 'Coding Challenge',
    },
    'frontend-interview': {
      icon: Code,
      color: 'text-cyan-600 bg-cyan-100 dark:bg-cyan-900/20',
      borderColor: 'border-cyan-200 dark:border-cyan-800',
      label: 'Frontend Challenge',
    },
    'multiple-choice': {
      icon: Target,
      color: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800',
      label: 'Multiple Choice',
    },
    'written-interview': {
      icon: FileText,
      color: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      borderColor: 'border-orange-200 dark:border-orange-800',
      label: 'Written Question',
    },
  };

  return (
    typeMap[round] || {
      icon: MessageSquare,
      color: 'text-gray-600 bg-gray-100 dark:bg-gray-900/20',
      borderColor: 'border-gray-200 dark:border-gray-800',
      label: 'Question',
    }
  );
};

// Helper function to capitalize first letter
const capitalizeFirstLetter = (string: string) => {
  return string?.charAt(0)?.toUpperCase() + string?.slice(1);
};

// Helper function to decode base64 template files for Sandpack
const getTemplate = (template: any[]) => {
  const arrayToObject = {};

  if (!template || !Array.isArray(template)) {
    console.warn('Invalid template data:', template);
    return {};
  }

  template.forEach(({ fileName, fileData }) => {
    try {
      if (fileName && fileData) {
        arrayToObject[fileName] = base64.decode(fileData);
      }
    } catch (error) {
      console.error(`Error decoding file ${fileName}:`, error);
    }
  });

  return arrayToObject;
};

// Sidebar component for Sandpack file explorer
const SideBar = () => {
  const [activeTab, setActiveTab] = useState('');

  return (
    <>
      <div className="flex w-12 flex-col gap-4 p-2">
        <div
          onClick={() =>
            activeTab === 'file-explore' ? setActiveTab('') : setActiveTab('file-explore')
          }
          className={`cursor-pointer hover:text-gray-500 ${
            activeTab === 'file-explore' ? 'text-gray-500' : ''
          }`}
        >
          <Icon name="Files" className="h-6 w-6" />
        </div>
      </div>
      {activeTab === 'file-explore' ? (
        <SandpackFileExplorer
          style={{
            flex: '0 0 150px',
          }}
        />
      ) : null}
    </>
  );
};

// Question Detail View Component for the main content area
const QuestionDetailView = ({
  question,
  index,
  userId,
}: {
  question: any;
  index: number;
  userId?: string;
}) => {
  const { theme } = useTheme();

  const typeInfo = getQuestionTypeInfo(question?.round);
  const IconComponent = typeInfo.icon;

  const isAnswered =
    question?.isAnswered ||
    question?.answer ||
    question?.status ||
    question?.memory ||
    question?.time;

  // Get video URL from question data
  const videoUrl = question?.videoUrl;

  // Get source code for coding questions
  const initialSourceCode = question?.source_code;
  const sourceCode =
    initialSourceCode && question?.round === 'coding-interview'
      ? base64.decode(initialSourceCode)
      : '';

  return (
    <Card className={cn('h-fit', typeInfo.borderColor)}>
      {/* Question Header */}
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className={cn('rounded-lg p-2', typeInfo.color)}>
            <IconComponent className="h-5 w-5" />
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <CardTitle className="text-xl">Question {index + 1}</CardTitle>
              <Badge variant="outline" className="text-xs">
                {typeInfo.label}
              </Badge>
              {isAnswered ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Question Content */}
        {question?.question && (
          <div>
            <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
              Question
            </h3>
            <CardDescription className="text-sm leading-relaxed">
              {question.question}
            </CardDescription>
          </div>
        )}

        {/* Question Description */}
        {question?.questionDescription && (
          <div>
            <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
              Description
            </h3>
            {question?.round === 'frontend-interview' ? (
              <CardDescription className="prose prose-sm dark:prose-invert">
                <ReactMarkdown>{question.questionDescription.replace(/\\n/g, '\n')}</ReactMarkdown>
              </CardDescription>
            ) : (
              <CardDescription className="text-sm leading-relaxed">
                {question.questionDescription}
              </CardDescription>
            )}
          </div>
        )}

        {/* Answer/Response Section */}
        {isAnswered ? (
          <div className="space-y-4">
            {/* Video Response */}
            {question?.round === 'video-interview' && (
              <div>
                <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Video Response
                </h3>
                {videoUrl ? (
                  <VideoPlayer src={videoUrl} />
                ) : (
                  <div className="flex h-48 items-center justify-center rounded-lg border bg-gray-50 dark:bg-gray-900">
                    <div className="text-center">
                      <Play className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                      <p className="text-sm text-gray-500">Video response available</p>
                    </div>
                  </div>
                )}
                {question?.isAnswered && (
                  <div className="mt-4">
                    <InterviewQuestionTiming questions={[question]} activeIndex={0} />
                  </div>
                )}
              </div>
            )}

            {/* Text Answer/Transcript */}
            {question?.answer && (
              <div>
                <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Transcript
                </h3>
                <Card className="bg-gray-50 dark:bg-gray-900/50">
                  <CardContent className="p-4">
                    <CardDescription className="text-sm leading-relaxed">
                      {question.answer}
                    </CardDescription>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Coding Results */}
            {question?.round === 'coding-interview' && question?.status && (
              <div>
                <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Execution Results
                </h3>
                <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                  <CodingResultStatusCard
                    title="Language"
                    description={capitalizeFirstLetter(question?.language || 'N/A')}
                  />
                  <CodingResultStatusCard title="Time" description={question?.time || '-'} />
                  <CodingResultStatusCard title="Memory" description={question?.memory || '-'} />
                  <CodingResultStatusCard title="Status" description={question?.status || '-'} />
                </div>
              </div>
            )}

            {/* Coding Source Code Display */}
            {question?.round === 'coding-interview' && question?.source_code && (
              <div>
                <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Submitted Code
                </h3>
                <Card className="bg-gray-50 dark:bg-gray-900/50">
                  <CardContent className="p-4">
                    <pre className="overflow-x-auto whitespace-pre-wrap text-sm">
                      <code>{base64.decode(question.source_code)}</code>
                    </pre>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Frontend Sandbox */}
            {question?.round === 'frontend-interview' && (
              <div>
                <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Frontend Implementation
                </h3>
                {question?.source_code?.files && question?.source_code?.files?.length > 0 ? (
                  <div className="h-[600px] w-full overflow-hidden rounded-lg border">
                    <SandpackProvider
                      key={`Sandpack${index}`}
                      template={question?.source_code?.templateName || 'react'}
                      theme={theme === 'dark' ? 'dark' : 'light'}
                      options={{ autoReload: true }}
                      customSetup={{
                        dependencies: question?.source_code?.dependencies || {},
                        environment: question?.source_code?.environment || 'create-react-app',
                      }}
                      files={getTemplate(question?.source_code?.files)}
                    >
                      <SandpackLayout style={{ height: '100%' }}>
                        <ResizablePanelGroup direction="horizontal" className="h-full">
                          <ResizablePanel defaultSize={60} minSize={40}>
                            <div className="flex h-full">
                              <SideBar />
                              <Separator orientation="vertical" />
                              <SandpackCodeEditor
                                showTabs
                                showLineNumbers={true}
                                showInlineErrors
                                wrapContent
                                closableTabs
                                style={{ height: '100%', flex: 1 }}
                                readOnly={true}
                                showReadOnly={false}
                              />
                            </div>
                          </ResizablePanel>
                          <ResizableHandle withHandle />
                          <ResizablePanel defaultSize={40} minSize={30}>
                            <SandpackPreview
                              showOpenInCodeSandbox={false}
                              style={{ height: '100%' }}
                              showNavigator
                            />
                          </ResizablePanel>
                        </ResizablePanelGroup>
                      </SandpackLayout>
                    </SandpackProvider>
                  </div>
                ) : (
                  <Card className="bg-gray-50 dark:bg-gray-900/50">
                    <CardContent className="p-8 text-center">
                      <Code className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                      <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
                        No Implementation Available
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        {question?.isAnswered
                          ? "The candidate's implementation could not be loaded."
                          : "Candidate didn't attempt this frontend question."}
                      </p>

                      {/* Debug Information - Remove in production */}
                      <details className="mt-4 text-left">
                        <summary className="cursor-pointer text-xs text-gray-400">
                          Debug Info
                        </summary>
                        <pre className="mt-2 max-h-32 overflow-auto text-xs text-gray-600">
                          {JSON.stringify(
                            {
                              isAnswered: question?.isAnswered,
                              hasSourceCode: !!question?.source_code,
                              hasFiles: !!question?.source_code?.files,
                              filesLength: question?.source_code?.files?.length,
                              templateName: question?.source_code?.templateName,
                              environment: question?.source_code?.environment,
                              filesPreview: question?.source_code?.files?.slice(0, 2),
                            },
                            null,
                            2,
                          )}
                        </pre>
                      </details>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Performance and Feedback */}
            {question?.feedback && (
              <div className="space-y-4">
                <div className="border-t pt-4">
                  <h3 className="mb-4 text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Assessment & Feedback
                  </h3>
                  <div className="space-y-4">
                    <PerformanceScoreSection data={question.feedback} />

                    {question.feedback.short_summary && (
                      <FeedBackSection
                        short_summary={question.feedback.short_summary}
                        title="Overall Feedback"
                      />
                    )}

                    {question.feedback.strengths && (
                      <FeedBackSection
                        short_summary={question.feedback.strengths}
                        title="Strengths"
                      />
                    )}

                    {question.feedback.areas_of_improvement && (
                      <FeedBackSection
                        short_summary={question.feedback.areas_of_improvement}
                        title="Areas for Improvement"
                      />
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-32 items-center justify-center rounded-lg border bg-gray-50 dark:bg-gray-900">
            <div className="text-center">
              <XCircle className="mx-auto mb-2 h-8 w-8 text-gray-400" />
              <p className="text-sm text-gray-500">Candidate didn&apos;t attempt this question</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const ModernQuestionsSection = ({
  questions,
  careerPractice,
  userId,
}: ModernQuestionsSectionProps) => {
  const [activeQuestionIndex, setActiveQuestionIndex] = useState(0);

  if (!questions || questions.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Questions Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            No interview questions found for this session.
          </p>
        </CardContent>
      </Card>
    );
  }

  const activeQuestion = questions[activeQuestionIndex];

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center gap-2">
        <MessageSquare className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold">Interview Questions</h3>
        <Badge variant="outline">{questions.length} Questions</Badge>
      </div>

      {/* Playlist Layout */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Questions Playlist - Left Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Questions Playlist
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[600px] overflow-y-auto">
                {questions.map((question, index) => {
                  const typeInfo = getQuestionTypeInfo(question?.round);
                  const IconComponent = typeInfo.icon;
                  const isActive = index === activeQuestionIndex;
                  const isAnswered =
                    question?.isAnswered ||
                    question?.answer ||
                    question?.status ||
                    question?.memory ||
                    question?.time;

                  return (
                    <div
                      key={index}
                      onClick={() => setActiveQuestionIndex(index)}
                      className={cn(
                        'flex cursor-pointer items-center gap-3 border-b border-gray-100 p-4 transition-colors hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50',
                        isActive &&
                          'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20',
                      )}
                    >
                      <div className={cn('rounded-lg p-2', typeInfo.color)}>
                        <IconComponent className="h-4 w-4" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <span
                            className={cn(
                              'text-sm font-medium',
                              isActive
                                ? 'text-blue-900 dark:text-blue-100'
                                : 'text-gray-900 dark:text-gray-100',
                            )}
                          >
                            Question {index + 1}
                          </span>
                          {isAnswered ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <XCircle className="h-3 w-3 text-red-500" />
                          )}
                        </div>
                        <p
                          className={cn(
                            'truncate text-xs',
                            isActive
                              ? 'text-blue-700 dark:text-blue-300'
                              : 'text-gray-500 dark:text-gray-400',
                          )}
                        >
                          {typeInfo.label}
                        </p>
                        {question?.question && (
                          <p
                            className={cn(
                              'mt-1 truncate text-xs',
                              isActive
                                ? 'text-blue-600 dark:text-blue-400'
                                : 'text-gray-400 dark:text-gray-500',
                            )}
                          >
                            {question.question}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Question Content - Right Main Area */}
        <div className="lg:col-span-2">
          <QuestionDetailView
            question={activeQuestion}
            index={activeQuestionIndex}
            userId={userId}
          />
        </div>
      </div>
    </div>
  );
};
