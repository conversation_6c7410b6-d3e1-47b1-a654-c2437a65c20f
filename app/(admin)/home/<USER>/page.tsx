import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import HomeBottomCard from '@/components/cards/home-bottom-card';
import { authOptions } from '@/lib/auth';
import { getInterviewathonDetail } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const filter = props?.searchParams?.filter ?? 'last1hour';
  const interviewPage = props?.searchParams?.interviewathonPage ?? 1;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const completedInterview = await getInterviewathonDetail(
    tenantId,
    'completedTime',
    filter,
    interviewPage,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <HomeBottomCard
      title={'Recent Interviewathon'}
      totalCount={completedInterview?.totalCount}
      screen="interviewathon"
      tabs={[
        {
          title: 'Completed',
          value: 'completed',
          header: ['Student/Interviewathon', 'Score', 'Completed Time'],
          row: completedInterview?.eventDetails?.map((item) => {
            return {
              id: item?.id,
              eventId: item?.eventDetails?.id,
              interview: item?.eventDetails?.name,
              score: item?.feedback?.overall_score,
              candidate:
                item?.user?.userProfile?.fullName !== '' || item?.user?.userProfile?.fullName
                  ? item?.user?.userProfile?.fullName
                  : item?.user?.email,
              time: item?.completedTime,
            };
          }),
        },
      ]}
      page={interviewPage}
    />
  );
}

function formatTimestamp(
  timestamp: string,
  includeSeconds: boolean = false,
): {
  date: string;
  time: string;
} {
  const date = new Date(timestamp);

  const optionsDate: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };

  let optionsTime: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: 'numeric',
  };

  if (includeSeconds) {
    optionsTime = { ...optionsTime, second: 'numeric' };
  }

  const formattedDate = date.toLocaleDateString('en-US', optionsDate);
  const formattedTime = date.toLocaleTimeString('en-US', optionsTime);

  return {
    date: formattedDate,
    time: formattedTime,
  };
}
