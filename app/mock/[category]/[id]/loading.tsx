import QuestionCard from '@/components/skeleton/question-card';

import { Card } from '@camped-ui/card';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  return (
    <div className="flex w-full flex-col gap-4 md:flex-row">
      <div className="w-full max-w-[640px]" style={{ flex: 2 }}>
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Skeleton className="mt-2 h-4 w-full" />
        <Card className="mt-3 h-[300px] p-4"></Card>
      </div>

      <div style={{ flex: 1 }} className="w-full pb-4 md:max-w-[320px]">
        <QuestionCard />
        <div className="pt-4"></div>
        <QuestionCard />
      </div>
    </div>
  );
}
