import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

interface RequestParams {
  params: {
    model: string;
  };
}

export async function POST(request: Request, { params }: RequestParams) {
  const model = params?.model;
  const tenantId = request.headers.get('tenant-id');
  const input = await request.json();

  if (!tenantId) {
    return NextResponse.json({ error: 'Unauthorized: Tenant ID is missing.' }, { status: 401 });
  }

  if (!model || !db[model]) {
    return NextResponse.json(
      { error: `Model "${model}" does not exist or is invalid.` },
      { status: 404 },
    );
  }

  console.log({input})

  try {
    const response = await db[model].create({
      data: input,
    });

    console.log({response})

    return NextResponse.json(
      {
        data: response,
        message: 'Record retrieved successfully.',
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Database operation failed:', error);
    return NextResponse.json(
      { error: 'Internal Server Error: Failed to fetch data.' },
      { status: 500 },
    );
  }
}
