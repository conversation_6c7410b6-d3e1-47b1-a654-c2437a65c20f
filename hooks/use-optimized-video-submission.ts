import { useCallback, useState, useRef } from 'react';
import { uploadVideoInChunks } from '@/utils/chunked-upload';
import { v4 as uuid } from 'uuid';
import toast from 'react-hot-toast';

interface VideoSubmissionState {
  isCompressing: boolean;
  isUploading: boolean;
  isProcessingFeedback: boolean;
  compressionProgress: number;
  uploadProgress: number;
  currentStep: 'idle' | 'compressing' | 'uploading' | 'processing-feedback' | 'completed' | 'error';
  error: string | null;
  videoId: string | null;
  canContinue: boolean; // Whether user can continue with other actions
}

interface SubmissionOptions {
  organizationId?: string;
  screenName: string;
  onUploadComplete?: (videoId: string) => void;
  onFeedbackComplete?: (response: any) => void;
  onError?: (error: string) => void;
  backgroundMode?: boolean; // Whether to process in background
}

export const useOptimizedVideoSubmission = () => {
  const [state, setState] = useState<VideoSubmissionState>({
    isCompressing: false,
    isUploading: false,
    isProcessingFeedback: false,
    compressionProgress: 0,
    uploadProgress: 0,
    currentStep: 'idle',
    error: null,
    videoId: null,
    canContinue: false,
  });

  const workerRef = useRef<Worker | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Compress video using Web Worker (non-blocking)
  const compressVideoAsync = useCallback(async (
    blob: Blob,
    unique_id: string,
    screenName: string
  ): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      // Create worker if it doesn't exist
      if (!workerRef.current) {
        workerRef.current = new Worker(
          new URL('../utils/video-compression-worker.ts', import.meta.url)
        );
      }

      const worker = workerRef.current;

      worker.onmessage = (event) => {
        const { type, progress, compressedBlob, error } = event.data;

        switch (type) {
          case 'progress':
            setState(prev => ({ ...prev, compressionProgress: progress }));
            break;
          case 'completed':
            resolve(compressedBlob);
            break;
          case 'error':
            reject(new Error(error));
            break;
        }
      };

      worker.onerror = (error) => {
        reject(error);
      };

      // Start compression
      worker.postMessage({
        type: 'compress',
        file: blob,
        unique_id,
        mimeType: 'video/mp4',
        screenName,
      });
    });
  }, []);

  // Main submission function
  const submitVideo = useCallback(async (
    recordedChunks: Blob[],
    options: SubmissionOptions
  ) => {
    try {
      setState(prev => ({
        ...prev,
        isCompressing: true,
        isUploading: false,
        isProcessingFeedback: false,
        currentStep: 'compressing',
        error: null,
        compressionProgress: 0,
        uploadProgress: 0,
        canContinue: false,
      }));

      // Create abort controller for this submission
      abortControllerRef.current = new AbortController();

      const id = uuid();
      const unique_id = `${id}.mp4`;

      // Create blob from recorded chunks
      const blob = new Blob(recordedChunks, { type: 'video/mp4' });

      // Step 1: Compress video in Web Worker (non-blocking)
      const compressedFile = await compressVideoAsync(blob, unique_id, options.screenName);

      setState(prev => ({
        ...prev,
        isCompressing: false,
        isUploading: true,
        currentStep: 'uploading',
        compressionProgress: 100,
      }));

      // Step 2: Get signed URL for upload
      const response = await fetch(
        `/api/gcp-bucket?id=${unique_id}${options.organizationId ? `&organizationId=${options.organizationId}` : ''}`,
        {
          method: 'GET',
          signal: abortControllerRef.current.signal,
        }
      );

      if (!response.ok) {
        throw new Error('Failed to get upload URL');
      }

      const data = await response.json();
      const uploadUrl = data.signedUrl;

      // Step 3: Upload with chunking and progress
      const uploadResult = await uploadVideoInChunks(compressedFile, uploadUrl, {
        onProgress: (progress) => {
          setState(prev => ({ ...prev, uploadProgress: progress }));
        },
        maxRetries: 3,
      });

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Upload failed');
      }

      // Step 4: Upload completed
      setState(prev => ({
        ...prev,
        isUploading: false,
        uploadProgress: 100,
        videoId: unique_id,
        canContinue: options.backgroundMode || false,
      }));

      options.onUploadComplete?.(unique_id);

      // If background mode, user can continue while feedback processes
      if (options.backgroundMode) {
        setState(prev => ({ ...prev, currentStep: 'completed', canContinue: true }));
      

        // Process feedback in background
        processFeedbackInBackground(unique_id, options);
      } else {
        // Process feedback immediately
        await processFeedback(unique_id, options);
      }

      return { success: true, videoId: unique_id };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Video submission failed';

      setState(prev => ({
        ...prev,
        isCompressing: false,
        isUploading: false,
        isProcessingFeedback: false,
        currentStep: 'error',
        error: errorMessage,
        canContinue: false,
      }));

      options.onError?.(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [compressVideoAsync]);

  // Process feedback (can be called immediately or in background)
  const processFeedback = useCallback(async (
    videoId: string,
    options: SubmissionOptions
  ) => {
    setState(prev => ({
      ...prev,
      isProcessingFeedback: true,
      currentStep: 'processing-feedback',
    }));

    try {
      // Note: The actual feedback processing will be handled by the calling component
      // This hook just manages the state and UI feedback
      // The onFeedbackComplete callback should handle the actual API calls

      setState(prev => ({
        ...prev,
        isProcessingFeedback: false,
        currentStep: 'completed',
      }));

      // Call the completion callback with the videoId
      options.onFeedbackComplete?.({ videoId, success: true });

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Feedback processing failed';

      setState(prev => ({
        ...prev,
        isProcessingFeedback: false,
        currentStep: 'error',
        error: errorMessage,
      }));

      options.onError?.(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  // Process feedback in background (non-blocking)
  const processFeedbackInBackground = useCallback(async (
    videoId: string,
    options: SubmissionOptions
  ) => {
    // This runs in background, user can continue with other actions
    setTimeout(async () => {
      const result = await processFeedback(videoId, options);
      if (result.success) {
        
      } else {
        toast.error('Video analysis failed. Please try again.');
      }
    }, 100); // Small delay to ensure UI updates
  }, [processFeedback]);

  const abortSubmission = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isCompressing: false,
      isUploading: false,
      isProcessingFeedback: false,
      currentStep: 'idle',
      error: 'Submission cancelled',
      canContinue: false,
    }));

    toast.error('Video submission cancelled');
  }, []);

  const resetState = useCallback(() => {
    setState({
      isCompressing: false,
      isUploading: false,
      isProcessingFeedback: false,
      compressionProgress: 0,
      uploadProgress: 0,
      currentStep: 'idle',
      error: null,
      videoId: null,
      canContinue: false,
    });
  }, []);

  // Calculate overall progress
  const overallProgress = state.currentStep === 'compressing'
    ? state.compressionProgress * 0.4 // Compression is 40% of the process
    : state.currentStep === 'uploading'
    ? 40 + (state.uploadProgress * 0.5) // Upload is 50% of the process
    : state.currentStep === 'processing-feedback'
    ? 90 // Show 90% while processing feedback
    : state.currentStep === 'completed'
    ? 100
    : 0;

  const isProcessing = state.isCompressing || state.isUploading || state.isProcessingFeedback;

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
  }, []);

  return {
    ...state,
    overallProgress,
    isProcessing,
    submitVideo,
    abortSubmission,
    resetState,
    cleanup,
  };
};
