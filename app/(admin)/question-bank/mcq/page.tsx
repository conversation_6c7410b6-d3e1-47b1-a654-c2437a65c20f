import { cookies } from 'next/headers';

import MCQQuestionBankScreen from '@/components/wrapper-screen/question-bank/mcq-question-bank-screen';
import { getQuizCategory, getQuizQuestion } from '@/services/apicall';

export default async function Page(props) {
  //   const session: any = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const selectedCategory = props?.searchParams?.category;

  const [question, category] = await Promise.all([
    getQuizQuestion({ organizationId: tenantId, category: selectedCategory }),
    getQuizCategory(),
  ]);
  return (
    <MCQQuestionBankScreen
      questions={question?.result}
      category={category}
      type="mcq"
      selectedCategory={selectedCategory}
    />
  );
}
