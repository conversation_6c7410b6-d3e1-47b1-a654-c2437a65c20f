import { Card } from '@camped-ui/card';
import { Skeleton } from '@camped-ui/skeleton';

export default function Loading() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-4 w-44" />
      <Skeleton className="h-2 w-3/4" />
      <Skeleton className="h-2 w-1/4" />
      <Skeleton className="h-4 w-44" />
      <Skeleton className="h-2 w-3/4" />
      <Skeleton className="h-2 w-1/4" />
      <Skeleton className="h-4 w-44" />
      <div className="mt-3 grid grid-cols-4 gap-4">
        <Card className="h-28 p-5">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="mt-3 h-4 w-2/4" />
        </Card>
        <Card className="h-28 p-5">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="mt-3 h-4 w-2/4" />
        </Card>
        <Card className="h-28 p-5">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="mt-3 h-4 w-2/4" />
        </Card>
        <Card className="h-28 p-5">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="mt-3 h-4 w-2/4" />
        </Card>
      </div>
      <Skeleton className="h-4 w-44" />
      <Card className="p-4">
        <Skeleton className="h-4" />
        <Skeleton className="mt-2 h-4" />
        <Skeleton className="mt-2 h-4 w-1/4" />
      </Card>{' '}
      <Skeleton className="h-4 w-44" />
      <Card className="p-4">
        <Skeleton className="h-4" />
        <Skeleton className="mt-2 h-4" />
        <Skeleton className="mt-2 h-4 w-1/4" />
      </Card>
    </div>
  );
}
