import { cookies } from 'next/headers';

import { FrontEndCodingTable } from '@/components/data-table/frontend-practice/data-table';
import { getFrontEndPractice, getFrontEndPracticeByPage } from '@/services/apicall';

export type DocumentsPageProps = {
  searchParams?: {
    page?: string;
    per_page?: string;
    eventName?: string;
  };
};
export default async function FrontendPractice({ searchParams = {} }: DocumentsPageProps) {
  const userId = cookies()?.get('aceprepUserId')?.value;
  const problems = await getFrontEndPracticeByPage(searchParams);
  return <FrontEndCodingTable tasksPromise={{ data: problems }} userId={userId} />;
}
