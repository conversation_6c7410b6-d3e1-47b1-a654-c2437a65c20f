import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import MyCodingInterviewCard from '@/components/cards/coding-card';
import { EdgeCaseCard } from '@/components/cards/edge-case';
import { authOptions } from '@/lib/auth';
import { getMyCodingPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview() {
  const userId = cookies().get('aceprepUserId')?.value;

  if (!userId) {
    return redirect('/');
  }

  const codingPractices = await getMyCodingPractice(userId, null);

  if (codingPractices?.length === 0) {
    return (
      <EdgeCaseCard
        title="Sharpen Your Coding Skills"
        description="Take advantage of our Coding Practice section to enhance your coding abilities and prepare effectively for technical interviews. Our tailored coding challenges are designed to align with your career goals, providing detailed feedback to help you improve and master problem-solving skills."
        ctaLabel="Start Coding Practice Now"
        ctaHref="/coding-problems"
      />
    );
  }

  const session = await getServerSession(authOptions);
  return (
    <>
      {codingPractices?.map((coding, index) => (
        <MyCodingInterviewCard coding={coding} key={index} session={session} />
      ))}
    </>
  );
}
